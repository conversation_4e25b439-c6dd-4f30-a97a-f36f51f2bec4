import streamlit as st
import pandas as pd
from db.fetch_tod_tab_data import (
    fetch_tod_binned_data,
    fetch_combined_monthly_data,
    fetch_all_daily_tod_data,
    fetch_daily_tod_data,
    fetch_hourly_generation_data,
    fetch_datetime_tod_data
)
from visualizations.tod_tab_visual import (
    create_monthly_before_banking_plot,
    create_monthly_before_banking_plot_interactive,
    create_monthly_banking_settlement_chart,
    create_monthly_banking_settlement_chart_interactive,
    create_tod_binned_plot,
    create_tod_binned_plot_interactive,
    create_tod_generation_plot,
    create_tod_generation_plot_interactive,
    create_tod_consumption_plot,
    create_tod_consumption_plot_interactive,
    create_mean_trend_vs_irregularities_plot,
    create_monthly_settled_heatmap,
    create_monthly_settled_heatmap_interactive,
    create_tod_line_chart,
    create_tod_line_chart_interactive,
    create_tod_monthly_generation_consumption_charts
)
from db.db_setup import CONN
from frontend.ui_components.dashboard_controls import get_interactive_plot_setting


def validate_inputs(selected_plant, start_date=None, end_date=None):
    """Validate common inputs and return error message if invalid."""
    if not selected_plant:
        return "⚠️ Please select a plant to view the data."
    if start_date is not None and not start_date:
        return "⚠️ Please select a date range to view the data."
    if end_date is not None and not end_date:
        return "⚠️ Please select both start and end dates."
    return None


def display_chart(fig, use_interactive=False):
    """Display chart with error handling."""
    if fig:
        if use_interactive:
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.pyplot(fig)
    else:
        st.warning("⚠️ Unable to generate the chart.")





def display_monthly_tod_before_banking(selected_plant):
    """Display monthly ToD before banking chart."""
    error_msg = validate_inputs(selected_plant)
    if error_msg:
        st.warning(error_msg)
        return
    
    try:
        df = fetch_all_daily_tod_data(CONN, selected_plant)
        if df is None or df.empty:
            st.warning("📊 No monthly ToD data available for the selected plant.")
            return
        
        use_interactive = get_interactive_plot_setting()
        
        if use_interactive:
            fig = create_monthly_before_banking_plot_interactive(df, selected_plant)
        else:
            fig = create_monthly_before_banking_plot(df, selected_plant)
            
        display_chart(fig, use_interactive)
        
    except Exception:
        st.error("❌ Unable to retrieve or display data. Please try again.")


def display_monthly_banking_settlement(selected_plant):
    """Display monthly banking settlement chart."""
    error_msg = validate_inputs(selected_plant)
    if error_msg:
        st.warning(error_msg)
        return
    
    try:
        df = fetch_combined_monthly_data(CONN, selected_plant)
        if df is None or df.empty:
            st.warning("📊 No monthly banking settlement data available.")
            return
        
        use_interactive = get_interactive_plot_setting()
        
        if use_interactive:
            fig = create_monthly_banking_settlement_chart_interactive(df, selected_plant)
        else:
            fig, _ = create_monthly_banking_settlement_chart(df, selected_plant)
            
        display_chart(fig, use_interactive)
        
        # Display metrics
        _display_banking_settlement_metrics(df)
        
    except Exception:
        st.error("❌ Unable to retrieve or display banking settlement data.")


def _display_banking_settlement_metrics(df):
    """Display banking settlement metrics."""
    try:
        # Calculate metrics
        df['settlement_with_banking'] = df['total_intra_settlement'] + df['total_inter_settlement']
        df['total_settlement'] = df['settlement_with_banking'] + df['total_matched_settled_sum']
        df['surplus_demand_after_banking'] = df['surplus_demand_sum_after_inter'].clip(lower=0)
        
        # Calculate totals
        total_generation_mwh = df['total_generation_sum'].sum() / 1000 if 'total_generation_sum' in df.columns else 0
        total_consumption_mwh = df['total_consumption_sum'].sum() / 1000 if 'total_consumption_sum' in df.columns else 0
        total_settlement_mwh = df['total_settlement'].sum() / 1000 if 'total_settlement' in df.columns else 0
        total_surplus_demand_after_banking_mwh = df['surplus_demand_after_banking'].sum() / 1000 if 'surplus_demand_after_banking' in df.columns else 0
        
        replacement_percentage = (total_settlement_mwh / total_consumption_mwh) * 100 if total_consumption_mwh > 0 else 0
        
        # Display metrics
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Total Generation (MWh)", f"{total_generation_mwh:.2f}")
        with col2:
            st.metric("Total Consumption (MWh)", f"{total_consumption_mwh:.2f}")
        with col3:
            st.metric("Replacement (with Banking) %", f"{replacement_percentage:.2f}%")
        with col4:
            st.metric("Surplus Demand (after Banking)", f"{total_surplus_demand_after_banking_mwh:.2f}")
            
    except Exception:
        st.warning("⚠️ Unable to calculate metrics.")


def display_tod_generation_vs_consumption(selected_plant, start_date, end_date=None):
    """Display ToD generation vs consumption plot."""
    error_msg = validate_inputs(selected_plant, start_date)
    if error_msg:
        st.warning(error_msg)
        return
    
    try:
        df = fetch_tod_binned_data(CONN, selected_plant, start_date, end_date)
        if df is None or df.empty:
            st.warning("📊 No ToD generation vs consumption data available.")
            return
        
        use_interactive = get_interactive_plot_setting()
        
        if use_interactive:
            fig = create_tod_binned_plot_interactive(df, selected_plant, start_date, end_date)
        else:
            fig = create_tod_binned_plot(df, selected_plant, start_date, end_date)
            
        display_chart(fig, use_interactive)
        
    except Exception:
        st.error("❌ Unable to retrieve or display ToD data.")


def display_tod_generation(selected_plant, start_date, end_date=None):
    """Display ToD generation plot."""
    error_msg = validate_inputs(selected_plant, start_date)
    if error_msg:
        st.warning(error_msg)
        return
    
    try:
        df = fetch_daily_tod_data(CONN, selected_plant, start_date, end_date)
        if df is None or df.empty:
            st.warning("📊 No ToD generation data available.")
            return
        
        use_interactive = get_interactive_plot_setting()
        
        if use_interactive:
            fig = create_tod_generation_plot_interactive(df, selected_plant, start_date, end_date)
        else:
            fig = create_tod_generation_plot(df, selected_plant, start_date, end_date)
            
        display_chart(fig, use_interactive)
        
    except Exception:
        st.error("❌ Unable to retrieve or display ToD generation data.")


def display_tod_consumption(selected_plant, start_date, end_date=None):
    """Display ToD consumption plot."""
    error_msg = validate_inputs(selected_plant, start_date)
    if error_msg:
        st.warning(error_msg)
        return
    
    try:
        df = fetch_daily_tod_data(CONN, selected_plant, start_date, end_date)
        if df is None or df.empty:
            st.warning("📊 No ToD consumption data available.")
            return
        
        use_interactive = get_interactive_plot_setting()
        
        if use_interactive:
            fig = create_tod_consumption_plot_interactive(df, selected_plant, start_date, end_date)
        else:
            fig = create_tod_consumption_plot(df, selected_plant, start_date, end_date)
            
        display_chart(fig, use_interactive)
        
    except Exception:
        st.error("❌ Unable to retrieve or display ToD consumption data.")


def display_tod_line_chart(selected_plant, start_date, end_date):
    """Display ToD line chart with options."""
    error_msg = validate_inputs(selected_plant, start_date, end_date)
    if error_msg:
        st.warning(error_msg)
        return
    
    # Chart type selection
    chart_type = st.radio(
        "Choose data to display:",
        ["Both", "Generation", "Consumption"],
        horizontal=True,
        key=f"tod_line_chart_{selected_plant}_{start_date}_{end_date}"
    )
    
    # TOD slot selection
    all_slots = ['Morning Peak', 'Day (Normal)', 'Evening Peak', 'Off-Peak']
    slot_cols = st.columns(len(all_slots))
    selected_slots = []
    
    for i, slot in enumerate(all_slots):
        with slot_cols[i]:
            if st.checkbox(slot, value=True, key=f"slot_{slot}_{selected_plant}_{start_date}_{end_date}"):
                selected_slots.append(slot)
    
    if not selected_slots:
        st.warning("⚠️ At least one TOD slot must be selected.")
        selected_slots = all_slots
    
    try:
        df = fetch_daily_tod_data(CONN, selected_plant, start_date, end_date)
        if df is None or df.empty:
            st.warning("📊 No ToD line chart data available.")
            return
        
        # Filter by selected slots
        df = df[df['slot'].isin(selected_slots)]
        if df.empty:
            st.warning(f"⚠️ No data available for selected slots: {', '.join(selected_slots)}")
            return
        
        use_interactive = get_interactive_plot_setting()
        chart_type_param = chart_type.lower()
        
        if use_interactive:
            fig = create_tod_line_chart_interactive(df, selected_plant, start_date, end_date, chart_type_param)
        else:
            fig = create_tod_line_chart(df, selected_plant, start_date, end_date, chart_type_param)
            
        display_chart(fig, use_interactive)
        
    except Exception:
        st.error("❌ Unable to retrieve or display ToD line chart data.")


def display_mean_trend_vs_irregularities(selected_plant, start_date, end_date):
    """Display mean trend vs irregularities plot."""
    error_msg = validate_inputs(selected_plant, start_date, end_date)
    if error_msg:
        st.warning(error_msg)
        return
    
    try:
        df = fetch_hourly_generation_data(CONN, selected_plant, start_date, end_date)
        if df is None or df.empty:
            st.warning("📊 No hourly generation data available.")
            return
        
        fig = create_mean_trend_vs_irregularities_plot(df, selected_plant)
        if fig:
            st.plotly_chart(fig, use_container_width=True)
            _display_trend_analysis_metrics(df, start_date, end_date)
        else:
            st.warning("⚠️ Unable to generate the chart.")
            
    except Exception:
        st.error("❌ Unable to retrieve or display trend analysis data.")


def _display_trend_analysis_metrics(df, start_date, end_date):
    """Display trend analysis metrics."""
    try:
        # Calculate metrics
        total_data_points = len(df)
        unique_hours = df['time'].nunique()
        date_range_days = (pd.to_datetime(end_date) - pd.to_datetime(start_date)).days + 1
        
        # Calculate irregularities
        trend_stats = df.groupby('time')['generation_kwh'].agg(['mean', 'std']).reset_index()
        trend_stats.columns = ['time', 'mean_gen', 'std_gen']
        
        df_with_stats = pd.merge(df, trend_stats, on='time', how='left')
        df_with_stats['upper_threshold'] = df_with_stats['mean_gen'] + 2 * df_with_stats['std_gen']
        df_with_stats['lower_threshold'] = df_with_stats['mean_gen'] - 2 * df_with_stats['std_gen']
        df_with_stats['is_irregular'] = (
            (df_with_stats['generation_kwh'] > df_with_stats['upper_threshold']) |
            (df_with_stats['generation_kwh'] < df_with_stats['lower_threshold'])
        )
        
        total_irregularities = df_with_stats['is_irregular'].sum()
        irregularity_percentage = (total_irregularities / total_data_points) * 100 if total_data_points > 0 else 0
        
        # Display metrics
        st.markdown("### 📊 Analysis Summary")
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("Total Data Points", f"{total_data_points:,}")
        with col2:
            st.metric("Time Periods", f"{unique_hours} hours")
        with col3:
            st.metric("Date Range", f"{date_range_days} days")
        with col4:
            st.metric("Irregularities", f"{total_irregularities} ({irregularity_percentage:.1f}%)")
            
    except Exception:
        st.warning("⚠️ Unable to calculate analysis metrics.")


def display_monthly_settled_heatmap(selected_plant):
    """Display monthly settled heatmap."""
    error_msg = validate_inputs(selected_plant)
    if error_msg:
        st.warning(error_msg)
        return
    
    try:
        df = fetch_all_daily_tod_data(CONN, selected_plant)
        if df is None or df.empty:
            st.warning("📊 No settled values data available.")
            return
        
        if 'settled' not in df.columns:
            st.warning("📊 Settled values data not available for this plant.")
            return
        
        use_interactive = get_interactive_plot_setting()
        
        if use_interactive:
            fig = create_monthly_settled_heatmap_interactive(df, selected_plant)
        else:
            fig = create_monthly_settled_heatmap(df, selected_plant)
            
        display_chart(fig, use_interactive)
        
    except Exception:
        st.error("❌ Unable to retrieve or display heatmap data.")


def display_tod_monthly_generation_consumption_charts(selected_plant):
    """Display ToD monthly generation vs consumption charts."""
    error_msg = validate_inputs(selected_plant)
    if error_msg:
        st.warning(error_msg)
        return
    
    try:
        df = fetch_datetime_tod_data(CONN, selected_plant)
        # Ensure datetime column is in datetime format
        df['datetime'] = pd.to_datetime(df['datetime'])

        # Filter for dates starting from April 1, 2025
        df = df[df['datetime'] >= pd.Timestamp('2025-04-01')]

        # (Optional) Reset index if needed
        df = df.reset_index(drop=True)
        if df is None or df.empty:
            st.warning("⚠️ No data available for the selected plant.")
            return
        
        fig = create_tod_monthly_generation_consumption_charts(df, selected_plant)
        if fig:
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.warning("⚠️ Unable to generate the heatmap.")
            
    except Exception:
        st.error("❌ Unable to retrieve or display heatmap data.")


