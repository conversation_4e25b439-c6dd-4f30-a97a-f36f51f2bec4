"""
Demo script to show how the interactive unit-wise monthly bill analysis works.

This demonstrates the new interactive table functionality where:
1. Users see a summary table with all consumption units when "All Units" is selected
2. Users can click on any consumption unit row to see detailed monthly breakdown
3. The interface provides metrics and detailed monthly data for the selected unit
"""

import streamlit as st
import pandas as pd
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the required functions
from frontend.display_plots.summary_display import display_interactive_summary_table
from visualizations.unit_wise_power_cost_calculations import (
    summarize_unitwise_costs_by_consumption_unit,
    summarize_unitwise_costs_table
)

def create_sample_data():
    """Create sample data for demonstration"""
    
    # Sample detailed unit-wise monthly data
    sample_data = pd.DataFrame({
        'Unit': ['Unit-A', 'Unit-A', 'Unit-A', 'Unit-B', 'Unit-B', 'Unit-B', 'Unit-C', 'Unit-C'],
        'Month': ['2024-01', '2024-02', '2024-03', '2024-01', '2024-02', '2024-03', '2024-01', '2024-02'],
        'Grid Cost (₹)': [50000, 45000, 52000, 75000, 68000, 72000, 30000, 28000],
        'Actual Cost (₹)': [35000, 32000, 37000, 52000, 48000, 50000, 21000, 20000],
        'Savings (₹)': [15000, 13000, 15000, 23000, 20000, 22000, 9000, 8000],
        'Energy Offset (kWh)': [7500, 6500, 7500, 11500, 10000, 11000, 4500, 4000],
        'Savings (%)': [30.0, 28.9, 28.8, 30.7, 29.4, 30.6, 30.0, 28.6]
    })
    
    return sample_data

def main():
    st.set_page_config(
        page_title="Interactive Unit-wise Bill Analysis Demo",
        page_icon="⚡",
        layout="wide"
    )
    
    st.title("⚡ Interactive Unit-wise Monthly Bill Analysis Demo")
    st.markdown("---")
    
    # Create sample data
    sample_detailed_data = create_sample_data()
    
    # Create summary table (aggregated by consumption unit)
    summary_table = summarize_unitwise_costs_by_consumption_unit(sample_detailed_data)
    
    st.subheader("📋 How it works:")
    st.markdown("""
    1. **Summary View**: Below you'll see a summary table showing aggregated data for each consumption unit
    2. **Interactive Selection**: Click on any consumption unit row (except GRAND TOTAL) to see detailed breakdown
    3. **Detailed View**: When you select a unit, you'll see:
       - Key metrics (Total Savings, Grid Cost, Actual Cost, Energy Offset)
       - Monthly breakdown table with all months for that unit
    4. **Navigation**: Use the "🔄 Back to Summary" button to return to the main view
    """)
    
    st.markdown("---")
    st.subheader("📊 Interactive Summary Table")
    
    # Display the interactive table
    display_interactive_summary_table(summary_table, sample_detailed_data, "consumption_unit")
    
    st.markdown("---")
    st.subheader("💡 Key Features:")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        **Interactive Features:**
        - ✅ Click-to-drill-down functionality
        - ✅ Formatted currency display
        - ✅ Color-coded savings percentages
        - ✅ Sortable columns
        - ✅ Professional styling
        """)
    
    with col2:
        st.markdown("""
        **Detailed View Features:**
        - ✅ Key metrics dashboard
        - ✅ Monthly breakdown table
        - ✅ Easy navigation back to summary
        - ✅ Comprehensive unit analysis
        - ✅ Visual performance indicators
        """)
    
    st.markdown("---")
    st.info("💡 **Integration**: This functionality is now integrated into your main dashboard's unit-wise monthly bill analysis section!")

if __name__ == "__main__":
    main()
