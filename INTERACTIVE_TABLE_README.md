# Interactive Unit-wise Monthly Bill Analysis

## Overview

The unit-wise monthly bill analysis now features an interactive table that allows users to drill down from summary data to detailed monthly breakdowns with just a click.

## How It Works

### 1. Summary View (Default)
When "All Units" is selected in the dropdown:
- Shows aggregated data for each consumption unit (`cons_unit`)
- Displays totals for Grid Cost, Actual Cost, Savings, Energy Offset, and Savings %
- Includes a GRAND TOTAL row for overall summary

### 2. Interactive Selection
- Click on any consumption unit row (except GRAND TOTAL)
- The table will expand to show detailed monthly breakdown for that unit
- No page refresh required - seamless user experience

### 3. Detailed View
When a consumption unit is selected:
- **Key Metrics Dashboard**: Shows 4 key metrics at the top
  - Total Savings (with percentage)
  - Grid Cost
  - Actual Cost  
  - Energy Offset
- **Monthly Details Table**: Complete month-by-month breakdown
- **Navigation**: "🔄 Back to Summary" button to return to main view

## Features

### Visual Enhancements
- **Currency Formatting**: Proper Indian Rupee formatting with commas
- **Color-coded Savings**: 
  - Green: ≥15% savings (High performance)
  - Yellow: 10-15% savings (Medium performance)
  - Red: <10% savings (Low performance)
- **Professional Styling**: Clean, modern interface with proper alignment

### User Experience
- **Single-click Selection**: No complex interactions required
- **Clear Visual Feedback**: Selected rows are highlighted
- **Intuitive Navigation**: Easy to understand flow
- **Responsive Design**: Works well on different screen sizes

## Technical Implementation

### Key Components
1. **`display_interactive_summary_table()`**: Main function handling the interactive table
2. **AgGrid Integration**: Uses `st_aggrid` for advanced table functionality
3. **Dynamic Data Filtering**: Real-time filtering based on user selection
4. **State Management**: Proper handling of selection states

### Dependencies
```python
from st_aggrid import AgGrid, GridOptionsBuilder, GridUpdateMode
```

### Configuration Options
- **Row Selection**: Single row selection enabled
- **Column Formatting**: Automatic currency and percentage formatting
- **Sorting & Filtering**: Built-in capabilities
- **Custom CSS**: Color-coded performance indicators

## Usage Example

```python
# In your dashboard code
if selected_view == "All Units":
    summary_table = summarize_unitwise_costs_by_consumption_unit(unitwise_costs)
    display_interactive_summary_table(summary_table, unitwise_costs, "consumption_unit")
```

## Benefits

### For Users
- **Faster Analysis**: Quick drill-down without page navigation
- **Better Insights**: Clear visual indicators for performance
- **Improved Workflow**: Seamless transition between summary and details

### For Developers
- **Reusable Component**: Can be adapted for other similar tables
- **Maintainable Code**: Clean separation of concerns
- **Extensible Design**: Easy to add new features

## Demo

Run the demo script to see the functionality in action:
```bash
streamlit run demo_interactive_table.py
```

## Integration Status

✅ **Integrated**: The functionality is now live in the main dashboard
- Location: `frontend/display_plots/summary_display.py`
- Function: `display_unitwise_monthly_bill_analysis()`
- Trigger: When "All Units" is selected in the unit dropdown

## Future Enhancements

Potential improvements for future versions:
- Multi-row selection for comparison
- Export functionality for selected data
- Advanced filtering options
- Chart integration in detailed view
- Mobile-optimized responsive design
