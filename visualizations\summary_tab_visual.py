import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.ticker import FuncFormatter
import pandas as pd
from matplotlib.patches import Patch
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import numpy as np
from .tod_config import SLOT_METADATA, get_slot_color_map, add_slot_labels_with_time

def format_thousands(x, _):
    """Format numbers with thousands separators"""
    if x is None:
        return '0'
    try:
        return f'{int(x):,}'
    except:
        return str(x) if x is not None else '0'

def get_hour_slot(hour):
    """Map hour (0-23) to time slot."""
    if not isinstance(hour, (int, float)) or not (0 <= hour <= 23):
        return "Day (Normal)"
    
    hour = int(hour)
    if 6 <= hour < 9:
        return "Morning Peak"
    elif 9 <= hour < 18:
        return "Day (Normal)"
    elif 18 <= hour < 22:
        return "Evening Peak"
    else:  # 22-23 and 0-5
        return "Night Off-Peak"

def get_hour_color(hour, metric_type='generation'):
    """Get color for hour based on time slot and metric type."""
    slot = get_hour_slot(hour)
    base_color = SLOT_METADATA.get(slot, {}).get("color", "#1f77b4")
    
    # For consumption, make it slightly darker
    if metric_type == 'consumption':
        try:
            hex_color = base_color.lstrip('#')
            rgb = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
            darkened_rgb = tuple(max(0, c - 30) for c in rgb)
            return f"#{darkened_rgb[0]:02x}{darkened_rgb[1]:02x}{darkened_rgb[2]:02x}"
        except:
            return "#d62728"
    
    return base_color

#####Generation VS Consumption


def plot_generation_vs_consumption(
    df: pd.DataFrame,
    plant_display_name: str,
    start_date: str,
    end_date: str,
    is_hourly_aggregation: bool = False
):
    """Area plot for generation vs. consumption with optional settled percentage plot."""
    
    if df is None or df.empty:
        return None
    
    # Validate required columns
    required_columns = ['generation', 'consumption']
    if not all(col in df.columns for col in required_columns):
        return None
    
    is_single_day = start_date == end_date

    # Process data for plotting
    df_processed = df.copy()
    
    # Aggregate to monthly if date range > 60 days
    if not is_single_day and not is_hourly_aggregation and 'date' in df_processed.columns:
        df_processed['date'] = pd.to_datetime(df_processed['date'])
        date_range = (df_processed['date'].max() - df_processed['date'].min()).days
        
        if date_range > 60:
            df_processed['month'] = df_processed['date'].dt.to_period('M').astype(str)
            agg_dict = {'generation': 'sum', 'consumption': 'sum'}
            
            # Add optional columns if they exist
            for col in ['deficit', 'surplus_demand', 'surplus_generation', 'settled']:
                if col in df_processed.columns:
                    agg_dict[col] = 'sum'
            
            df_processed = df_processed.groupby('month').agg(agg_dict).reset_index()
            df_processed['date'] = pd.to_datetime(df_processed['month'] + '-01')
            df_processed = df_processed.drop('month', axis=1)
    
    # Compute surplus columns if missing
    if 'surplus_generation' not in df_processed.columns:
        df_processed['surplus_generation'] = (df_processed['generation'] - df_processed['consumption']).clip(lower=0)
    if 'surplus_demand' not in df_processed.columns:
        df_processed['surplus_demand'] = (df_processed['consumption'] - df_processed['generation']).clip(lower=0)
    
    # Create figure
    fig, ax = plt.subplots(figsize=(16, 8))

    # Set up x-axis and formatting
    if is_hourly_aggregation:
        if 'datetime' not in df_processed.columns:
            return None
        x = df_processed['datetime']
        ax.set_xlabel("Datetime (Hourly)", fontsize=12)
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%b %d\n%H:%M'))
        ax.xaxis.set_major_locator(mdates.AutoDateLocator())
        plt.setp(ax.get_xticklabels(), rotation=45, ha='right', fontweight='semibold', fontsize=10, color='dimgray', fontfamily='sans-serif')
    elif is_single_day:
        if 'datetime' not in df_processed.columns:
            return None
        x = df_processed['datetime']
        ax.set_xlabel("Time of Day", fontsize=12)
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        plt.setp(ax.get_xticklabels(), rotation=45, ha='right')
    else:
        if 'date' not in df_processed.columns:
            return None
        x = df_processed['date']
        date_range = (df_processed['date'].max() - df_processed['date'].min()).days
        
        if date_range > 60:
            ax.set_xlabel("Month", fontsize=12)
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%b %Y'))
            ax.xaxis.set_major_locator(mdates.MonthLocator())
        else:
            ax.set_xlabel("Date", fontsize=12)
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%b %d'))
            ax.xaxis.set_major_locator(mdates.WeekdayLocator(interval=2) if len(df_processed) > 30 else mdates.DayLocator(interval=2))
        plt.setp(ax.get_xticklabels(), rotation=45, ha='right')

    # Create main plots
    ax.plot(x, df_processed['generation'], color='green', linewidth=2.5, marker='o', markersize=6, label='Generation')
    ax.plot(x, df_processed['consumption'], color='red', linewidth=2.5, marker='s', markersize=6, linestyle='--', label='Consumption')

    # Add settled line if available
    if 'settled' in df_processed.columns:
        ax.plot(x, df_processed['settled'], color='blue', linewidth=2.5, marker='^', markersize=6, linestyle='-.', label='Settled')

    # Add area fills
    ax.fill_between(x, df_processed['generation'], df_processed['consumption'], 
                   where=(df_processed['generation'] >= df_processed['consumption']), 
                   color='green', alpha=0.3, interpolate=True, label='Surplus Generation')
    ax.fill_between(x, df_processed['generation'], df_processed['consumption'], 
                   where=(df_processed['generation'] < df_processed['consumption']), 
                   color='red', alpha=0.3, interpolate=True, label='Surplus Demand')

    # Title and formatting
    label = (
        f"Hourly Aggregation: {start_date} to {end_date}" if is_hourly_aggregation
        else start_date if is_single_day
        else f"{start_date} to {end_date}"
    )
    ax.set_title(f"Generation vs Consumption for {plant_display_name} ({label})", fontsize=16, fontweight='bold', pad=20)
    ax.set_ylabel("Energy (kWh)", fontsize=12)

    # Dynamic y-axis formatting
    if df[['generation', 'consumption']].max().max() > 1000:
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f'{x/1000:.1f}K' if x >= 1000 else f'{x:.0f}'))

    ax.legend(loc='upper right', fontsize=11)
    ax.grid(True, linestyle='--', alpha=0.4)
    plt.tight_layout()

    return fig




def plot_generation_vs_consumption_interactive(
    df: pd.DataFrame,
    plant_display_name: str,
    start_date: str,
    end_date: str,
    is_hourly_aggregation: bool = False
):
    """Interactive Plotly plot for Generation vs Consumption with surplus fill and settlement %."""

    if df is None or df.empty:
        return None

    required_columns = ['date', 'generation', 'consumption']
    if not all(col in df.columns for col in required_columns):
        return None

    df = df.copy()
    df['date'] = pd.to_datetime(df['date']).dt.to_period('M').dt.to_timestamp()

    is_single_day = start_date == end_date
    if is_hourly_aggregation:
        x_col, x_title, hover_fmt = 'datetime', "Datetime (Hourly)", '%b %d<br>%H:%M'
    elif is_single_day:
        x_col, x_title, hover_fmt = 'datetime', "Time of Day", '%H:%M'
    else:
        x_col, x_title, hover_fmt = 'date', "Month", '%b %Y'

    # Compute surplus and settlement
    df['surplus_generation'] = df['generation'] - df['consumption']
    df['surplus_demand'] = -df['surplus_generation']
    df['settlement_percentage'] = (
        df.get('settled', 0) / df['consumption'] * 100
    ).fillna(0).clip(upper=100)

    fig = make_subplots(
        rows=1, cols=1,
        subplot_titles=[f"Generation vs Consumption for {plant_display_name}"],
        specs=[[{"secondary_y": True}]]
    )

    def add_line(name, y, color, dash=None, symbol=None, secondary_y=False):
        fig.add_trace(go.Scatter(
            x=df[x_col],
            y=y,
            mode='lines+markers',
            name=name,
            line=dict(color=color, width=3, dash=dash),
            marker=dict(size=8, symbol=symbol),
            hovertemplate=f'<b>Date:</b> %{{x|{hover_fmt}}}<br><b>{name}:</b> %{{y:,.1f}}<extra></extra>'
        ), row=1, col=1, secondary_y=secondary_y)

    add_line("Generation", df['generation'], 'green')
    add_line("Consumption", df['consumption'], 'red', dash='dash')

    if 'settled' in df.columns:
        add_line("Settlement %", df['settlement_percentage'], 'blue', dash='dot', symbol='triangle-up', secondary_y=True)

    # Conditional area fills
    def add_surplus_area(upper, lower, fillcolor, name):
        fig.add_trace(go.Scatter(
            x=df[x_col], y=lower, fill=None, mode='lines',
            line=dict(color='rgba(0,0,0,0)'), showlegend=False, hoverinfo='skip'
        ))
        fig.add_trace(go.Scatter(
            x=df[x_col], y=upper, fill='tonexty', mode='lines',
            line=dict(color='rgba(0,0,0,0)'), fillcolor=fillcolor,
            name=name, hoverinfo='skip'
        ))

    add_surplus_area(
        np.where(df['surplus_generation'] > 0, df['generation'], df['consumption']),
        df['consumption'],
        'rgba(0,128,0,0.3)',
        'Surplus Generation'
    )

    add_surplus_area(
        np.where(df['surplus_demand'] > 0, df['consumption'], df['generation']),
        df['generation'],
        'rgba(255,0,0,0.3)',
        'Surplus Demand'
    )

    # Layout & Axes
    fig.update_layout(
        height=600,
        hovermode='closest',
        showlegend=True,
        margin=dict(t=80),
        legend=dict(orientation="h", yanchor="bottom", y=1.08, xanchor="right", x=1)
    )

    fig.update_xaxes(
        title_text=x_title,
        tickvals=df[x_col],
        ticktext=df[x_col].dt.strftime(hover_fmt),
        tickformat="%b %Y",
        showgrid=True,
        gridcolor='rgba(128,128,128,0.2)'
    )

    fig.update_yaxes(
        title_text="Energy (kWh)",
        showgrid=True,
        gridcolor='rgba(128,128,128,0.2)'
    )

    fig.update_yaxes(
        title_text="Settlement (%)",
        secondary_y=True,
        range=[0, 110],
        showgrid=False
    )

    return fig

def plot_generation_vs_consumption_hourly(
    df: pd.DataFrame,
    plant_display_name: str,
    start_date: str,
    end_date: str,
    is_hourly_aggregation: bool = False
):
    """Line plot for generation vs. consumption with time-of-day colored grid lines."""
    
    if df.empty:
        return None

    is_single_day = start_date == end_date

    # Calculate surplus columns
    if 'surplus_generation' not in df.columns:
        df['surplus_generation'] = (df['generation'] - df['consumption']).clip(lower=0)
    if 'surplus_demand' not in df.columns:
        df['surplus_demand'] = (df['consumption'] - df['generation']).clip(lower=0)

    # Create figure
    if is_single_day or is_hourly_aggregation:
        fig, ax = plt.subplots(figsize=(16, 8))
        axes = [ax]
    else:
        fig, axes = plt.subplots(2, 1, figsize=(16, 12), gridspec_kw={'height_ratios': [3, 1]})
        ax = axes[0]

    # Set up x-axis
    if is_single_day:
        x = df['datetime']
        ax.set_xlabel("Time of Day", fontsize=12)
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
    elif is_hourly_aggregation:
        x = df['hour']
        ax.set_xlabel("Hour of Day (across all selected days)", fontsize=12)
        ax.set_xticks(range(0, 24))
        ax.set_xticklabels([f"{h:02d}:00" for h in range(24)])
        plt.setp(ax.get_xticklabels(), rotation=30, ha='right', fontweight='semibold', 
                fontsize=11, color='dimgray', fontfamily='sans-serif')
    else:
        x = df['date']
        ax.set_xlabel("Date", fontsize=12)
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%b %d'))
        if len(df) > 30:
            ax.xaxis.set_major_locator(mdates.WeekdayLocator(interval=2))
        else:
            ax.xaxis.set_major_locator(mdates.DayLocator(interval=2))

    # Plot main lines
    ax.plot(x, df['generation'], color='green', linewidth=2.5, marker='o', markersize=6, label='Generation')
    ax.plot(x, df['consumption'], color='red', linewidth=2.5, marker='s', markersize=6, linestyle='--', label='Consumption')

    # Apply time-of-day color scheme for hourly aggregation
    if is_hourly_aggregation:
        # Add colored vertical grid lines at time slot transitions
        transition_hours = [6, 9, 18, 22]
        for hour in transition_hours:
            if hour < len(df):
                slot = get_hour_slot(hour)
                slot_color = SLOT_METADATA[slot]["color"]
                ax.axvline(x=hour, color=slot_color, linestyle='-', alpha=0.8, linewidth=2.5)
        
        # Fill areas with time-slot colors
        for hour in range(len(df) - 1):
            slot = get_hour_slot(hour)
            slot_color = SLOT_METADATA[slot]["color"]
            
            current_gen = df.iloc[hour]['generation']
            current_cons = df.iloc[hour]['consumption']
            next_gen = df.iloc[hour + 1]['generation']
            next_cons = df.iloc[hour + 1]['consumption']
            
            ax.fill_between([hour, hour + 1], [current_gen, next_gen], [current_cons, next_cons],
                           color=slot_color, alpha=0.3, interpolate=True)
        
        ax.grid(True, axis='y', linestyle='--', alpha=0.4)
    else:
        # Standard fill areas
        ax.fill_between(x, df['generation'], df['consumption'], 
                        where=(df['generation'] >= df['consumption']), 
                        color='green', alpha=0.3, interpolate=True, label='Surplus Generation')
        ax.fill_between(x, df['generation'], df['consumption'], 
                        where=(df['generation'] < df['consumption']), 
                        color='red', alpha=0.3, interpolate=True, label='Surplus Demand')

    # Set title and formatting
    label = f"Hourly Aggregation: {start_date} to {end_date}" if is_hourly_aggregation else (start_date if is_single_day else f"{start_date} to {end_date}")
    ax.set_title(f"Generation vs Consumption for {plant_display_name} ({label})", fontsize=16, fontweight='bold', pad=20)
    ax.set_ylabel("Energy (kWh)", fontsize=12)

    # Dynamic y-axis formatting
    if df[['generation', 'consumption']].max().max() > 1000:
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x/1000:.1f}K' if x >= 1000 else f'{x:.0f}'))

    ax.legend(loc='upper right', fontsize=11)
    
    if not is_hourly_aggregation:
        ax.grid(True, linestyle='--', alpha=0.4)
        plt.setp(ax.get_xticklabels(), rotation=45, ha='right')

    # Add settled percentage plot for multi-day non-hourly
    if not is_single_day and not is_hourly_aggregation and len(axes) > 1 and 'settled' in df.columns:
        ax2 = axes[1]
        df['settled_percentage'] = (100 * df['settled'] / df['consumption']).fillna(0)
        
        ax2.plot(df['date'], df['settled_percentage'], color='blue', linewidth=2.5, marker='d', markersize=6, label='Settled %')
        ax2.set_xlabel("Date", fontsize=12)
        ax2.set_ylabel("Settled (%)", fontsize=12)
        ax2.set_title("Daily Settled Percentage (Without Banking)", fontsize=14, fontweight='bold')
        ax2.grid(True, linestyle='--', alpha=0.4)
        ax2.xaxis.set_major_formatter(mdates.DateFormatter('%b %d'))
        
        if len(df) > 30:
            ax2.xaxis.set_major_locator(mdates.WeekdayLocator(interval=2))
        else:
            ax2.xaxis.set_major_locator(mdates.DayLocator(interval=2))
        
        ax2.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:.0f}%'))
        ax2.set_ylim(0, max(120, df['settled_percentage'].max() * 1.1))
        ax2.legend(loc='upper right', fontsize=10)
        plt.setp(ax2.get_xticklabels(), rotation=30, ha='right', fontweight='semibold')

    plt.tight_layout()
    if not is_single_day and not is_hourly_aggregation and len(axes) > 1:
        plt.subplots_adjust(hspace=0.3)

    return fig



def plot_generation_vs_consumption_hourly_interactive(
    df: pd.DataFrame,
    plant_display_name: str,
    start_date: str,
    end_date: str,
    is_hourly_aggregation: bool = False
):
    """Interactive Plotly version of generation vs. consumption with time-of-day colored areas."""
    
    if df is None or df.empty:
        return None

    # Validate required columns
    required_columns = ['generation', 'consumption']
    if not all(col in df.columns for col in required_columns):
        return None

    is_single_day = start_date == end_date
    
    # Calculate surplus columns
    if 'surplus_generation' not in df.columns:
        df['surplus_generation'] = (df['generation'] - df['consumption']).clip(lower=0)
    if 'surplus_demand' not in df.columns:
        df['surplus_demand'] = (df['consumption'] - df['generation']).clip(lower=0)
    
    # Add time slot information for hourly aggregation
    if is_hourly_aggregation and 'hour' in df.columns:
        df['time_slot'] = df['hour'].apply(get_hour_slot)
        df['formatted_hour'] = df['hour'].map(lambda h: f"{h:02d}:00")
    
    # Create figure
    fig = go.Figure()
    
    # Determine x-axis configuration
    if is_single_day:
        x_data = df['datetime']
        x_title = "Time of Day"
    elif is_hourly_aggregation:
        x_data = df['hour']
        x_title = "Hour of Day (across all selected days)"
    else:
        x_data = df['date']
        x_title = "Date"
        
    # Apply time-of-day color scheme for hourly aggregation
    if is_hourly_aggregation:
        # Add colored vertical grid lines at time slot transitions
        transition_hours = [6, 9, 18, 22]
        for hour in transition_hours:
            if hour < len(df):
                slot = get_hour_slot(hour)
                slot_color = SLOT_METADATA[slot]["color"]
                fig.add_vline(x=hour, line=dict(color=slot_color, width=2.5, dash='solid'), opacity=0.8)
        
        # Fill areas with time-slot colors
        slots_added_to_legend = set()
        for hour in range(len(df) - 1):
            slot = get_hour_slot(hour)
            slot_color = SLOT_METADATA[slot]["color"]
            
            current_gen = df.iloc[hour]['generation']
            current_cons = df.iloc[hour]['consumption']
            next_gen = df.iloc[hour + 1]['generation']
            next_cons = df.iloc[hour + 1]['consumption']
            
            show_in_legend = slot not in slots_added_to_legend
            if show_in_legend:
                slots_added_to_legend.add(slot)
            
            fig.add_trace(go.Scatter(
                x=[hour, hour + 1, hour + 1, hour],
                y=[current_gen, next_gen, next_cons, current_cons],
                fill='toself',
                fillcolor=slot_color,
                opacity=0.3,
                line=dict(width=0),
                showlegend=show_in_legend,
                hoverinfo='skip',
                name=f'{slot} ({SLOT_METADATA[slot]["time"]})',
                legendgroup=slot
            ))
        
        # Set custom x-axis ticks
        fig.update_xaxes(
            tickmode='array',
            tickvals=list(range(0, 24)),
            ticktext=[f"{h:02d}:00" for h in range(24)],
            tickangle=-30
        )
    else:
        # Standard fill areas for non-hourly aggregation
        surplus_gen_indices = df['generation'] >= df['consumption']
        if surplus_gen_indices.any():
            fig.add_trace(go.Scatter(x=x_data, y=df['generation'], fill=None, mode='lines', line=dict(width=0), showlegend=False, hoverinfo='skip'))
            fig.add_trace(go.Scatter(x=x_data, y=df['consumption'], fill='tonexty', fillcolor='rgba(0, 128, 0, 0.3)', mode='lines', line=dict(width=0), name='Surplus Generation', showlegend=True, hoverinfo='skip'))
        
        surplus_demand_indices = df['generation'] < df['consumption']
        if surplus_demand_indices.any():
            fig.add_trace(go.Scatter(x=x_data, y=df['consumption'], fill=None, mode='lines', line=dict(width=0), showlegend=False, hoverinfo='skip'))
            fig.add_trace(go.Scatter(x=x_data, y=df['generation'], fill='tonexty', fillcolor='rgba(255, 0, 0, 0.3)', mode='lines', line=dict(width=0), name='Surplus Demand', showlegend=True, hoverinfo='skip'))
    
    # Prepare hover templates and custom text based on data type
    if is_hourly_aggregation:
        # For hourly aggregation, create custom hover text with HH:MM format
        hover_text_gen = [f"<b>{hour:02d}:00</b><br><b>Generation:</b> {gen:.1f} kWh" 
                         for hour, gen in zip(df['hour'], df['generation'])]
        hover_text_cons = [f"<b>{hour:02d}:00</b><br><b>Consumption:</b> {cons:.1f} kWh" 
                          for hour, cons in zip(df['hour'], df['consumption'])]
        generation_hover = None
        consumption_hover = None
    elif is_single_day:
        # For single day, show time
        generation_hover = '<b>Time:</b> %{x|%H:%M}<br><b>Generation:</b> %{y:.1f} kWh<extra></extra>'
        consumption_hover = '<b>Time:</b> %{x|%H:%M}<br><b>Consumption:</b> %{y:.1f} kWh<extra></extra>'
        hover_text_gen = None
        hover_text_cons = None
    else:
        # For multi-day, show date
        generation_hover = '<b>Date:</b> %{x|%b %d}<br><b>Generation:</b> %{y:.1f} kWh<extra></extra>'
        consumption_hover = '<b>Date:</b> %{x|%b %d}<br><b>Consumption:</b> %{y:.1f} kWh<extra></extra>'
        hover_text_gen = None
        hover_text_cons = None

    # Add main traces
    if is_hourly_aggregation:
        # Use custom hover text for hourly aggregation
        fig.add_trace(go.Scatter(
            x=x_data,
            y=df['generation'],
            mode='lines+markers',
            name='Generation',
            line=dict(color='green', width=2.5),
            marker=dict(size=6, symbol='circle'),
            hovertext=hover_text_gen,
            hoverinfo='text'
        ))
        
        fig.add_trace(go.Scatter(
            x=x_data,
            y=df['consumption'],
            mode='lines+markers',
            name='Consumption',
            line=dict(color='red', width=2.5, dash='dash'),
            marker=dict(size=6, symbol='square'),
            hovertext=hover_text_cons,
            hoverinfo='text'
        ))
    else:
        # Use hovertemplate for other cases
        fig.add_trace(go.Scatter(
            x=x_data,
            y=df['generation'],
            mode='lines+markers',
            name='Generation',
            line=dict(color='green', width=2.5),
            marker=dict(size=6, symbol='circle'),
            hovertemplate=generation_hover
        ))
        
        fig.add_trace(go.Scatter(
            x=x_data,
            y=df['consumption'],
            mode='lines+markers',
            name='Consumption',
            line=dict(color='red', width=2.5, dash='dash'),
            marker=dict(size=6, symbol='square'),
            hovertemplate=consumption_hover
        ))
    
    # Update layout
    label = f"Hourly Aggregation: {start_date} to {end_date}" if is_hourly_aggregation else (start_date if is_single_day else f"{start_date} to {end_date}")
    
    fig.update_layout(
        title=f"Generation vs Consumption for {plant_display_name} ({label})",
        xaxis_title=x_title,
        yaxis_title="Energy (kWh)",
        hovermode='closest',
        template='plotly_white',
        height=600,
        showlegend=True,
        legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
        margin=dict(t=140)
    )
    
    # Dynamic y-axis formatting
    max_value = df[['generation', 'consumption']].max().max()
    if max_value > 1000:
        fig.update_yaxes(tickformat='.1s')
    
    return fig


##Generation

def create_generation_only_plot(df, plant_name, start_date, end_date=None, is_hourly_aggregation=False):
    if df.empty or 'generation' not in df.columns:
        fig, ax = plt.subplots(figsize=(10, 5))
        ax.text(0.5, 0.5, "No data available", ha='center', va='center', fontsize=12)
        return fig

    is_single_day = end_date is None or start_date == end_date
    fig, ax = plt.subplots(figsize=(12, 6))

    if is_single_day:
        x = df['datetime']
        ax.plot(x, df['generation'], color='green', marker='o', linewidth=2)
        ax.set_xlabel("Time of Day")
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
    elif is_hourly_aggregation:
        x = df['hour']
        bars = ax.bar(x, df['generation'], color='green', alpha=0.8)

        # Add values inside bars
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width() / 2, height * 0.5, f"{int(height):,}",
                   ha='center', va='center', fontsize=9, rotation=90, color='white')

        ax.set_xlabel("Hour of Day (across all selected days)")
        ax.set_xticks(range(0, 24))
        ax.set_xticklabels([f"{h:02d}:00" for h in range(24)])
        plt.setp(ax.get_xticklabels(), rotation=30, ha='right', fontweight='semibold',
                fontsize=11, color='dimgray', fontfamily='sans-serif')
    else:
        x = df['date']
        bars = ax.bar(x, df['generation'], color='green', alpha=0.8)

        # Add values inside bars
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width() / 2, height * 0.5, f"{int(height):,}",
                   ha='center', va='center', fontsize=9, rotation=90, color='white')

        ax.set_xlabel("Date")
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%d-%b'))
        ax.xaxis.set_major_locator(mdates.DayLocator(interval=2))
        plt.setp(ax.get_xticklabels(), rotation=30, ha='right', fontweight='semibold',
                fontsize=11, color='dimgray', fontfamily='sans-serif')

    ax.set_ylabel("Generation (kWh)")
    
    # Set title
    if is_hourly_aggregation:
        title = f"Hourly Aggregated Generation - {plant_name}\n{start_date} to {end_date}"
    else:
        title = f"Generation - {plant_name}\n{start_date}"
        if not is_single_day:
            title += f" to {end_date}"
    
    ax.set_title(title, fontsize=14, fontweight='bold')
    ax.yaxis.set_major_formatter(FuncFormatter(format_thousands))
    ax.grid(True, linestyle='--', alpha=0.6)
    plt.tight_layout()

    return fig



# ##Consumption
def create_consumption_plot(df, plant_name, start_date, end_date=None, is_hourly_aggregation=False):
    fig, ax = plt.subplots(figsize=(12, 6))

    if df.empty or 'consumption' not in df.columns:
        ax.text(0.5, 0.5, 'No consumption data available', ha='center', va='center', fontsize=12, color='red')
        ax.set_title(f"Consumption - {plant_name}", fontsize=14, fontweight='bold')
        return fig

    is_single_day = end_date is None or start_date == end_date

    if is_single_day:
        df['datetime'] = pd.to_datetime(df['datetime'])
        df = df.groupby('datetime', as_index=False)['consumption'].sum()
        ax.plot(df['datetime'], df['consumption'], color='red', marker='o', linewidth=2)
        ax.set_xlabel("Time of Day")
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        ax.xaxis.set_major_locator(mdates.HourLocator(interval=2))
        title = f"Consumption - {plant_name}\n{start_date}"
    elif is_hourly_aggregation:
        bars = ax.bar(df['hour'], df['consumption'], color='red', alpha=0.8)

        # Add values inside bars
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width() / 2, height * 0.5, f"{int(height):,}",
                   ha='center', va='center', fontsize=9, rotation=90, color='black')

        ax.set_xlabel("Hour of Day (across all selected days)")
        ax.set_xticks(range(0, 24))
        ax.set_xticklabels([f"{h:02d}:00" for h in range(24)])
        plt.setp(ax.get_xticklabels(), rotation=30, ha='right', fontweight='semibold',
                fontsize=11, color='dimgray', fontfamily='sans-serif')
        title = f"Hourly Aggregated Consumption - {plant_name}\n{start_date} to {end_date}"
    else:
        df = df.groupby('date', as_index=False)['consumption'].sum()
        bars = ax.bar(df['date'], df['consumption'], color='red', alpha=0.8)

        # Add values inside bars
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width() / 2, height * 0.5, f"{int(height):,}",
                   ha='center', va='center', fontsize=9, rotation=90, color='black')

        ax.set_xlabel("Date")
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%d-%b'))
        ax.xaxis.set_major_locator(mdates.DayLocator(interval=2))
        title = f"Daily Consumption - {plant_name}\n{start_date}"

    ax.set_title(title, fontsize=14, fontweight='bold')
    ax.set_ylabel("Consumption (kWh)")
    ax.yaxis.set_major_formatter(FuncFormatter(format_thousands))
    ax.grid(True, linestyle='--', alpha=0.6)
    
    # Only rotate labels for date plots, not for hourly
    if not is_hourly_aggregation:
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')
    
    plt.tight_layout()

    return fig





def plot_consumption_and_generation_pie(df: pd.DataFrame, plant_display_name: str, start_date_str: str, end_date_str: str):
    """
    Create two pie charts side by side:
    - Consumption distribution by cons_unit  
    - Allocated generation distribution by cons_unit
    """
    if df is None or df.empty:
        return None

    # Validate required columns
    required_columns = ['cons_unit', 'consumption', 'allocated_generation']
    if not all(col in df.columns for col in required_columns):
        return None

    fig, axes = plt.subplots(1, 2, figsize=(18, 8))

    # Define color palette
    distinct_colors = [
        '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', 
        '#F7DC6F', '#BB8FCE', '#85C1E9', '#F8C471', '#82E0AA', '#F1948A', '#85929E',
        '#D5DBDB', '#AED6F1', '#A9DFBF', '#F9E79F', '#D2B4DE', '#AEB6BF', '#FADBD8',
        '#D1F2EB', '#FCF3CF', '#EBDEF0', '#EAF2F8', '#E8F8F5', '#FEF9E7', '#F4ECF7',
        '#154360', '#0E6655', '#7D6608', '#633974', '#2E86AB', '#A23B72', '#F18F01',
        '#C73E1D', '#2F3061', '#4056A1', '#D79922', '#EFE2BA', '#F13C20', '#4056A1'
    ]
    
    # Aggregate data by cons_unit
    pie_data = df.groupby('cons_unit').agg({
        'consumption': 'sum',
        'allocated_generation': 'sum'
    }).reset_index()

    if pie_data.empty:
        return None
    
    # Get all unique cons_units across both datasets
    consumption_data = df.groupby('cons_unit')['consumption'].sum()
    generation_data = df.groupby('cons_unit')['allocated_generation'].sum()
    all_units = list(set(consumption_data.index.tolist() + generation_data.index.tolist()))
    
    # Ensure we have enough colors
    if len(all_units) > len(distinct_colors):
        import matplotlib.cm as cm
        import numpy as np
        additional_colors = cm.Set3(np.linspace(0, 1, len(all_units) - len(distinct_colors)))
        distinct_colors.extend(['#%02x%02x%02x' % (int(r*255), int(g*255), int(b*255)) for r, g, b, a in additional_colors])
    
    # Create color mapping for consistent colors across both charts
    color_map = {unit: distinct_colors[i] for i, unit in enumerate(all_units)}
    
    # Consumption pie chart
    consumption_colors = [color_map[unit] for unit in consumption_data.index]
    axes[0].pie(
        consumption_data, 
        labels=consumption_data.index,
        colors=consumption_colors,
        autopct='%1.1f%%',
        startangle=140,
        wedgeprops={'edgecolor': 'w', 'linewidth': 1.5}
    )
    
    # Format date range for title
    date_range = start_date_str if end_date_str is None or start_date_str == end_date_str else f"{start_date_str} to {end_date_str}"
    axes[0].set_title(f'Consumption Distribution by unit\n{plant_display_name}\n({date_range})', fontsize=14, fontweight='bold')

    # Allocated generation pie chart
    generation_colors = [color_map[unit] for unit in generation_data.index]
    axes[1].pie(
        generation_data, 
        labels=generation_data.index,
        colors=generation_colors,
        autopct='%1.1f%%',
        startangle=140,
        wedgeprops={'edgecolor': 'w', 'linewidth': 1.5}
    )
    axes[1].set_title(f'Allocated Generation Distribution by unit\n{plant_display_name}\n({date_range})', fontsize=14, fontweight='bold')

    plt.tight_layout()
    return fig

from typing import Optional

def plot_consumption_and_generation_pie_interactive(
    df: pd.DataFrame,
    plant_display_name: str,
    start_date: str,
    end_date: str
) -> Optional[go.Figure]:
    """
    Create an interactive Plotly figure with side-by-side pie charts for
    consumption and generation distribution across consumer units.
    """
    # Validate data
    if df.empty:
        return None

    # Validate required columns
    required_cols = ['cons_unit', 'consumption', 'allocated_generation']
    if not all(col in df.columns for col in required_cols):
        return None

    # Aggregate consumption and generation by consumer unit
    consumption_df = df.groupby('cons_unit', as_index=False)['consumption'].sum()
    generation_df = df.groupby('cons_unit', as_index=False)['allocated_generation'].sum()

    # Filter out rows with zero totals
    consumption_df = consumption_df[consumption_df['consumption'] > 0]
    generation_df = generation_df[generation_df['allocated_generation'] > 0]

    if consumption_df.empty and generation_df.empty:
        return None

    # Prepare figure layout with two pie charts
    fig = make_subplots(
        rows=1, cols=2,
        specs=[[{"type": "pie"}, {"type": "pie"}]],
        subplot_titles=[
            "Consumption Distribution by Consumer Unit",
            "Generation Distribution by Consumer Unit"
        ],
        horizontal_spacing=0.15
    )

    # Define color palette and mapping
    color_palette = [
        '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', 
        '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9', '#F8C471', '#82E0AA',
        '#F1948A', '#85929E', '#D5DBDB', '#AED6F1', '#A9DFBF', '#F9E79F',
        '#D2B4DE', '#AEB6BF', '#FADBD8', '#D1F2EB', '#FCF3CF', '#EBDEF0'
    ]

    all_units = list(set(consumption_df['cons_unit']).union(generation_df['cons_unit']))
    color_map = {unit: color_palette[i % len(color_palette)] for i, unit in enumerate(all_units)}

    # Helper for creating pie chart traces
    def create_pie_trace(data: pd.DataFrame, value_col: str, title: str, total: float) -> go.Pie:
        data['percentage'] = (data[value_col] / total * 100).round(1)
        hover_text = (
            "<b>Unit:</b> " + data['cons_unit'] + "<br>" +
            f"<b>{title}:</b> " + data[value_col].map(lambda x: f"{x:,.0f}") + " kWh<br>" +
            "<b>Share:</b> " + data['percentage'].map(lambda x: f"{x:.1f}%") + "<br>" +
            f"<b>Total:</b> {total:,.0f} kWh"
        ).tolist()
        return go.Pie(
            labels=data['cons_unit'],
            values=data[value_col],
            marker=dict(
                colors=[color_map[unit] for unit in data['cons_unit']],
                line=dict(color='white', width=2)
            ),
            hovertext=hover_text,
            hovertemplate='%{hovertext}<extra></extra>',
            textinfo='label+percent',
            textposition='auto',
            textfont=dict(size=11),
            name=title
        )

    # Add consumption pie chart
    if not consumption_df.empty:
        total_consumption = consumption_df['consumption'].sum()
        fig.add_trace(
            create_pie_trace(consumption_df, 'consumption', 'Consumption', total_consumption),
            row=1, col=1
        )

    # Add generation pie chart
    if not generation_df.empty:
        total_generation = generation_df['allocated_generation'].sum()
        fig.add_trace(
            create_pie_trace(generation_df, 'allocated_generation', 'Generation', total_generation),
            row=1, col=2
        )

    # Prepare date range for subtitle
    date_range = start_date if start_date == end_date else f"{start_date} to {end_date}"

    # Configure figure layout
    fig.update_layout(
        title={
            'text': f"Energy Distribution by Consumer Unit - {plant_display_name} ({date_range})",
            'x': 0.5, 'xanchor': 'center',
            'font': {'size': 16, 'family': 'Arial, sans-serif'}
        },
        font=dict(size=12, family='Arial, sans-serif'),
        legend=dict(
            orientation="v",
            yanchor="middle",
            y=0.5,
            xanchor="left",
            x=1.02,
            font=dict(size=11)
        ),
        margin=dict(l=20, r=150, t=80, b=20),
        height=600,
        hovermode='closest'
    )

    # Enhance subplot title annotations
    fig.update_annotations(font=dict(size=14, family='Arial, sans-serif', color='#2E86AB'))

    return fig



def create_merged_consumption_generation_table(
    df: pd.DataFrame, 
    plant_display_name: str
) -> pd.DataFrame:
    """
    Create a merged table combining consumption, generation, and location data.
    
    Args:
        df: DataFrame with columns - cons_unit, location_name, consumption, 
            allocated_generation, consumption_percentage, generation_percentage
        plant_display_name: Name of the plant for display purposes
    
    Returns:
        Formatted DataFrame for display
    """
    if df.empty:
        return pd.DataFrame()
    
    # Create a copy of the dataframe for formatting
    display_df = df.copy()
    
    # Format numeric columns for display
    display_df['consumption_formatted'] = display_df['consumption'].map(lambda x: f"{x:,.0f}")
    display_df['allocated_generation_formatted'] = display_df['allocated_generation'].map(lambda x: f"{x:,.0f}")
    display_df['consumption_percentage_formatted'] = display_df['consumption_percentage'].map(lambda x: f"{x:.1f}%")
    display_df['generation_percentage_formatted'] = display_df['generation_percentage'].map(lambda x: f"{x:.1f}%")
    
    # Select and rename columns for final display
    final_df = display_df[[
        'cons_unit', 
        'location_name',
        'consumption_formatted', 
        'consumption_percentage_formatted',
        'allocated_generation_formatted', 
        'generation_percentage_formatted'
    ]].copy()
    
    # Rename columns for display
    final_df.columns = [
        'Consumer Unit',
        'Location',
        'Consumption (kWh)',
        'Consumption %',
        'Generation (kWh)', 
        'Generation %'
    ]
    
    return final_df


def plot_tod_generation_consumption_lines(
    df: pd.DataFrame,
    plant_display_name: str,
    start_date: str,
    end_date: str = None,
    is_interactive: bool = True
):
    """
    Create a line chart showing generation and consumption for each ToD slot.
    This creates 8 lines total: 4 for generation (one per slot) and 4 for consumption (one per slot).
    """
    if df is None or df.empty:
        return None
        
    # Validate required columns - handle both 'date' and 'datetime' columns
    required_columns = ['slot', 'generation_kwh', 'consumption_kwh']
    date_column = None
    
    # Check for date or datetime column
    if 'date' in df.columns:
        date_column = 'date'
        required_columns.append('date')
    elif 'datetime' in df.columns:
        date_column = 'datetime'
        required_columns.append('datetime')
    else:
        return None
    
    # Validate all required columns exist
    if not all(col in df.columns for col in required_columns):
        return None
    
    # Ensure date column is datetime
    try:
        df[date_column] = pd.to_datetime(df[date_column])
    except:
        return None
    
    # Validate numeric columns
    numeric_columns = ['generation_kwh', 'consumption_kwh']
    for col in numeric_columns:
        if col in df.columns:
            try:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            except:
                return None
    
    # Determine x-axis column
    x_axis_column = date_column
    
    # Get all unique slots and validate them
    all_slots = df['slot'].unique()
    valid_slots = [slot for slot in all_slots if slot in SLOT_METADATA]
    
    if not valid_slots:
        return None
        
    # Filter out invalid slots from dataframe
    df = df[df['slot'].isin(valid_slots)]
    
    # Define custom legend order as requested
    desired_order = ["Morning Peak", "Day (Normal)", "Evening Peak", "Night Off-Peak"]
    # Only include slots that exist in the data, in the desired order
    valid_slots = [slot for slot in desired_order if slot in valid_slots]
    
    if df.empty:
        return None
    
    # Get slot colors
    slot_colors = get_slot_color_map()
        
    if is_interactive:
        fig = go.Figure()
        
        # Add generation and consumption lines for each slot
        for slot in valid_slots:
            slot_data = df[df['slot'] == slot].copy()
            
            if slot_data.empty:
                continue
                
            slot_color = slot_colors.get(slot, '#1f77b4')
            
            # Determine hover template based on granularity
            hover_date_format = '%Y-%m-%d %H:%M' if x_axis_column == 'datetime' else '%Y-%m-%d'
            hover_label = 'DateTime' if x_axis_column == 'datetime' else 'Date'
            
            # Generation line
            fig.add_trace(go.Scatter(
                x=slot_data[x_axis_column],
                y=slot_data['generation_kwh'],
                mode='lines',
                name=f'{slot} - Generation',
                line=dict(color=slot_color, width=2.5),
                hovertemplate=f'<b>{hover_label}:</b> %{{x|{hover_date_format}}}<br>' +
                            f'<b>Slot:</b> {slot}<br>' +
                            f'<b>Generation:</b> %{{y:.1f}} kWh<extra></extra>',
                legendgroup=slot,
                legendgrouptitle_text=f"{slot} ({SLOT_METADATA[slot]['time']})"
            ))

            # Consumption line (dashed)
            fig.add_trace(go.Scatter(
                x=slot_data[x_axis_column],
                y=slot_data['consumption_kwh'],
                mode='lines',
                name=f'{slot} - Consumption',
                line=dict(color=slot_color, width=2.5, dash='dash'),
                hovertemplate=f'<b>{hover_label}:</b> %{{x|{hover_date_format}}}<br>' +
                            f'<b>Slot:</b> {slot}<br>' +
                            f'<b>Consumption:</b> %{{y:.1f}} kWh<extra></extra>',
                legendgroup=slot
            ))
        
        # Update layout
        date_range_str = start_date if start_date == end_date else f"{start_date} to {end_date}"
        x_axis_title = "DateTime" if x_axis_column == 'datetime' else "Date"
        
        fig.update_layout(
            title=f"ToD Generation vs Consumption - {plant_display_name} ({date_range_str})",
            xaxis_title=x_axis_title,
            yaxis_title="Energy (kWh)",
            hovermode='closest',
            template='plotly_white',
            height=600,
            showlegend=True,
            legend=dict(
                orientation="v",
                yanchor="top",
                y=0.98,
                xanchor="left",
                x=1.02,
                font=dict(size=10),
                tracegroupgap=10
            ),
            margin=dict(l=60, r=200, t=80, b=60)
        )
        
        # Update x-axis formatting based on granularity
        if x_axis_column == 'datetime':
            date_range_days = (pd.to_datetime(end_date) - pd.to_datetime(start_date)).days
            
            if date_range_days <= 1:
                fig.update_xaxes(tickformat='%H:%M', dtick=3600000)
            elif date_range_days <= 7:
                fig.update_xaxes(tickformat='%m-%d %H:%M')
            else:
                fig.update_xaxes(tickformat='%m-%d')
        else:
            fig.update_xaxes(tickformat='%Y-%m-%d')
        
        # Dynamic y-axis formatting for large values
        max_value = df[['generation_kwh', 'consumption_kwh']].max().max()
        if max_value > 1000:
            fig.update_yaxes(tickformat='.1s')
            
        return fig
        
    else:
        # Static matplotlib version
        fig, ax = plt.subplots(figsize=(14, 8))
        
        # Add generation and consumption lines for each slot
        for slot in valid_slots:
            slot_data = df[df['slot'] == slot].copy()
            
            if slot_data.empty:
                continue
                
            slot_color = slot_colors.get(slot, '#1f77b4')
            
            # Generation line
            ax.plot(slot_data[x_axis_column], slot_data['generation_kwh'], 
                   color=slot_color, linewidth=2.5, marker='o', markersize=6,
                   label=f'{slot} - Generation', alpha=0.8)
            
            # Consumption line (dashed)
            ax.plot(slot_data[x_axis_column], slot_data['consumption_kwh'], 
                   color=slot_color, linewidth=2.5, marker='s', markersize=6,
                   linestyle='--', label=f'{slot} - Consumption', alpha=0.8)
        
        # Format x-axis based on granularity
        if x_axis_column == 'datetime':
            date_range_days = (pd.to_datetime(end_date) - pd.to_datetime(start_date)).days
            
            if date_range_days <= 1:
                ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
                ax.xaxis.set_major_locator(mdates.HourLocator(interval=2))
            elif date_range_days <= 7:
                ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
                ax.xaxis.set_major_locator(mdates.HourLocator(interval=6))
            else:
                ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
                ax.xaxis.set_major_locator(mdates.DayLocator())
        else:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%b %d'))
            ax.xaxis.set_major_locator(mdates.WeekdayLocator(interval=2) if len(df) > 14 else mdates.DayLocator())
        
        plt.setp(ax.get_xticklabels(), rotation=45, ha='right')
        
        # Labels and title
        date_range_str = start_date if start_date == end_date else f"{start_date} to {end_date}"
        ax.set_title(f"ToD Generation vs Consumption - {plant_display_name} ({date_range_str})", 
                    fontsize=16, fontweight='bold', pad=20)
        
        # Set axis labels
        x_axis_title = "DateTime" if x_axis_column == 'datetime' else "Date"
        ax.set_xlabel(x_axis_title, fontsize=12)
        ax.set_ylabel("Energy (kWh)", fontsize=12)
        
        # Dynamic y-axis formatting
        max_value = df[['generation_kwh', 'consumption_kwh']].max().max()
        if max_value > 1000:
            ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f'{x/1000:.1f}K' if x >= 1000 else f'{x:.0f}'))
        
        # Legend and grid
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=9)
        ax.grid(True, linestyle='--', alpha=0.4)
        
        plt.tight_layout()
        
        return fig


