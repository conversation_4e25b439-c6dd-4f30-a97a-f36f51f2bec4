import pandas as pd
from db.safe_db_utils import safe_read_sql
from helper.setup_logger import setup_logger



logging = setup_logger("fetch_summary_data", "fetch_summary_data.log")

# def fetch_generation_consumption_data(
#     conn,
#     client_name: str,
#     start_date: str,
#     end_date: str
# ) -> pd.DataFrame:
#     """
#     Fetch enriched generation and consumption data from settlement_data.

#     - Single day: return raw slot-wise rows without aggregation
#     - Multi-day: return daily aggregated totals

#     Returns:
#         pd.DataFrame with:
#         - datetime (for single day) or date (for multi-day)
#         - generation, consumption, deficit, surplus_demand, surplus_generation, settled
#     """
#     if conn is None:
#         return pd.DataFrame()

#     is_single_day = start_date == end_date

#     if is_single_day:
#         # No aggregation, raw records per slot
#         query = """
#             SELECT datetime,
#                    allocated_generation AS generation,
#                    consumption,
#                    deficit,
#                    surplus_demand,
#                    surplus_generation,
#                    settled
#             FROM settlement_data
#             WHERE client_name = %s AND date = %s
#             ORDER BY datetime;
#         """
#         df = safe_read_sql(query, conn, (client_name, start_date))
#         df['datetime'] = pd.to_datetime(df['datetime'])

#     else:
#         # Aggregate for each date
#         query = """
#             SELECT date,
#                    SUM(allocated_generation) AS generation,
#                    SUM(consumption) AS consumption,
#                    SUM(deficit) AS deficit,
#                    SUM(surplus_demand) AS surplus_demand,
#                    SUM(surplus_generation) AS surplus_generation,
#                    SUM(settled) AS settled
#             FROM settlement_data
#             WHERE client_name = %s AND date BETWEEN %s AND %s
#             GROUP BY date
#             ORDER BY date;
#         """
#         df = safe_read_sql(query, conn, (client_name, start_date, end_date))
#         df['date'] = pd.to_datetime(df['date'])

#     return df


def fetch_generation_consumption_data(
    conn,
    client_name: str,
    start_date: str,
    end_date: str,
    hourly_aggregation: bool = False
) -> pd.DataFrame:
    """
    Fetch enriched generation and consumption data from settlement_data.

    - Single day: return raw slot-wise rows without aggregation
    - Multi-day (hourly_aggregation=False): return daily aggregated totals
    - Multi-day (hourly_aggregation=True): return hourly aggregated totals across all days

    Returns:
        pd.DataFrame with:
        - datetime (for single day) or date (for multi-day daily) or hour (for multi-day hourly)
        - generation, consumption, deficit, surplus_demand, surplus_generation, settled
    """
    if conn is None:
        return pd.DataFrame()

    is_single_day = start_date == end_date

    if is_single_day:
        # No aggregation, raw records per slot
        query = """
            SELECT datetime,
                   allocated_generation AS generation,
                   consumption,
                   deficit,
                   surplus_demand,
                   surplus_generation,
                   settled
            FROM settlement_data
            WHERE client_name = %s AND date = %s
            ORDER BY datetime;
        """
        df = safe_read_sql(query, conn, (client_name, start_date))
        df['datetime'] = pd.to_datetime(df['datetime'])

    elif hourly_aggregation:
        # Aggregate by hour across all days
        query = """
            SELECT HOUR(datetime) AS hour,
                   SUM(allocated_generation) AS generation,
                   SUM(consumption) AS consumption,
                   SUM(deficit) AS deficit,
                   SUM(surplus_demand) AS surplus_demand,
                   SUM(surplus_generation) AS surplus_generation,
                   SUM(settled) AS settled
            FROM settlement_data
            WHERE client_name = %s AND date BETWEEN %s AND %s
            GROUP BY HOUR(datetime)
            ORDER BY hour;
        """
        df = safe_read_sql(query, conn, (client_name, start_date, end_date))
        
    else:
        # Aggregate for each date (original behavior)
        query = """
            SELECT date,
                   SUM(allocated_generation) AS generation,
                   SUM(consumption) AS consumption,
                   SUM(deficit) AS deficit,
                   SUM(surplus_demand) AS surplus_demand,
                   SUM(surplus_generation) AS surplus_generation,
                   SUM(settled) AS settled
            FROM settlement_data
            WHERE client_name = %s AND date BETWEEN %s AND %s
            GROUP BY date
            ORDER BY date;
        """
        df = safe_read_sql(query, conn, (client_name, start_date, end_date))
        df['date'] = pd.to_datetime(df['date'])

    return df



def fetch_unitwise_consumption_and_generation(
    conn,
    client_name: str,
    start_date: str,
    end_date: str
) -> pd.DataFrame:
    """
    Fetch aggregated consumption and allocated generation grouped by cons_unit
    for the specified client and date range.
    
    Returns:
        pd.DataFrame with columns:
        - cons_unit
        - consumption
        - allocated_generation
    """
    if conn is None:
        return pd.DataFrame()

    query = """
        SELECT cons_unit,
               SUM(consumption) AS consumption,
               SUM(allocated_generation) AS allocated_generation
        FROM settlement_data
        WHERE client_name = %s
          AND date BETWEEN %s AND %s
        GROUP BY cons_unit
        ORDER BY cons_unit;
    """
    
    df = safe_read_sql(query, conn, (client_name, start_date, end_date))

    # Safety: ensure numeric columns are of numeric type
    df['consumption'] = pd.to_numeric(df['consumption'], errors='coerce').fillna(0)
    df['allocated_generation'] = pd.to_numeric(df['allocated_generation'], errors='coerce').fillna(0)

    return df




def fetch_unitwise_consumption_and_generation(
    conn,
    client_name: str,
    start_date: str,
    end_date: str
) -> pd.DataFrame:
    """
    Fetch aggregated consumption and allocated generation grouped by cons_unit
    for the specified client and date range.
    
    Returns:
        pd.DataFrame with columns:
        - cons_unit
        - consumption
        - allocated_generation
    """
    if conn is None:
        return pd.DataFrame()

    query = """
        SELECT cons_unit,
               SUM(consumption) AS consumption,
               SUM(allocated_generation) AS allocated_generation
        FROM settlement_data
        WHERE client_name = %s
          AND date BETWEEN %s AND %s
        GROUP BY cons_unit
        ORDER BY cons_unit;
    """
    
    df = safe_read_sql(query, conn, (client_name, start_date, end_date))

    # Safety: ensure numeric columns are of numeric type
    df['consumption'] = pd.to_numeric(df['consumption'], errors='coerce').fillna(0)
    df['allocated_generation'] = pd.to_numeric(df['allocated_generation'], errors='coerce').fillna(0)

    return df


def fetch_unitwise_consumption_generation_with_location(
    conn,
    client_name: str,
    start_date: str,
    end_date: str
) -> pd.DataFrame:
    """
    Fetch aggregated consumption and allocated generation grouped by cons_unit
    with location information from consumption_mapping table.
    
    Returns:
        pd.DataFrame with columns:
        - cons_unit
        - location_name
        - consumption
        - allocated_generation
        - consumption_percentage
        - generation_percentage
    """
    if conn is None:
        return pd.DataFrame()

    query = """
        SELECT 
            sd.cons_unit,
            COALESCE(cm.location_name, 'Unknown Location') AS location_name,
            SUM(sd.consumption) AS consumption,
            SUM(sd.allocated_generation) AS allocated_generation
        FROM settlement_data sd
        LEFT JOIN consumption_mapping cm ON sd.client_name = cm.client_name 
                                        AND sd.cons_unit = cm.cons_unit
        WHERE sd.client_name = %s
          AND sd.date BETWEEN %s AND %s
        GROUP BY sd.cons_unit, cm.location_name
        ORDER BY sd.cons_unit;
    """
    
    df = safe_read_sql(query, conn, (client_name, start_date, end_date))

    if df.empty:
        return df

    # Safety: ensure numeric columns are of numeric type
    df['consumption'] = pd.to_numeric(df['consumption'], errors='coerce').fillna(0)
    df['allocated_generation'] = pd.to_numeric(df['allocated_generation'], errors='coerce').fillna(0)

    # Calculate additional metrics
    total_consumption = df['consumption'].sum()
    total_generation = df['allocated_generation'].sum()
    
    df['consumption_percentage'] = (df['consumption'] / total_consumption * 100) if total_consumption > 0 else 0
    df['generation_percentage'] = (df['allocated_generation'] / total_generation * 100) if total_generation > 0 else 0

    return df





def fetch_generation_consumption_daily_hourly(
    conn,
    client_name: str,
    start_date: str,
    end_date: str,
    hourly_aggregation: bool = False
) -> pd.DataFrame:
    """
    Fetch enriched generation and consumption data from settlement_data.

    Modes:
    - hourly_aggregation=True: returns hourly buckets for single or multiple days
    - single day raw slots: start_date == end_date, hourly_aggregation=False
    - multi-day daily aggregation: start_date != end_date, hourly_aggregation=False

    Returns:
        pd.DataFrame with:
        - datetime (hour) for hourly modes
        - datetime (slot) for single-day raw mode
        - date for multi-day daily aggregation
    """
    if conn is None:
        return pd.DataFrame()

    if hourly_aggregation:
        # Hourly buckets for single or multiple days
        query = """
    SELECT 
        DATE_FORMAT(datetime, '%Y-%m-%d %H:00:00') AS hour,
        SUM(allocated_generation) AS generation,
        SUM(consumption) AS consumption,
        SUM(deficit) AS deficit,
        SUM(surplus_demand) AS surplus_demand,
        SUM(surplus_generation) AS surplus_generation,
        SUM(settled) AS settled
    FROM settlement_data
    WHERE client_name = %s AND date BETWEEN %s AND %s
    GROUP BY hour
    ORDER BY hour;
"""

        df = safe_read_sql(query, conn, (client_name, start_date, end_date))
        df['datetime'] = pd.to_datetime(df['hour'])
        df.drop(columns='hour', inplace=True)

    else:
        is_single_day = start_date == end_date

        if is_single_day:
            # Raw, slot-wise records for single day
            query = """
                SELECT datetime,
                       allocated_generation AS generation,
                       consumption,
                       deficit,
                       surplus_demand,
                       surplus_generation,
                       settled
                FROM settlement_data
                WHERE client_name = %s AND date = %s
                ORDER BY datetime;
            """
            df = safe_read_sql(query, conn, (client_name, start_date))
            df['datetime'] = pd.to_datetime(df['datetime'])

        else:
            # Multi-day daily aggregation
            query = """
                SELECT date,
                       SUM(allocated_generation) AS generation,
                       SUM(consumption) AS consumption,
                       SUM(deficit) AS deficit,
                       SUM(surplus_demand) AS surplus_demand,
                       SUM(surplus_generation) AS surplus_generation,
                       SUM(settled) AS settled
                FROM settlement_data
                WHERE client_name = %s AND date BETWEEN %s AND %s
                GROUP BY date
                ORDER BY date;
            """
            df = safe_read_sql(query, conn, (client_name, start_date, end_date))
            df['date'] = pd.to_datetime(df['date'])

    return df


def fetch_generation_consumption_with_banking_settlement(
    conn,
    client_name: str,
    start_date: str,
    end_date: str
) -> pd.DataFrame:
    """
    Fetch generation and consumption data with banking settlement for monthly aggregation.
    
    This function combines:
    1. Monthly consumption and generation data from settlement_data
    2. Banking settlement data from banking_settlement table
    3. Calculates total_settlement = settlement_with_banking + total_matched_settled_sum
    
    Returns:
        pd.DataFrame with monthly aggregated data including:
        - month, generation, consumption, deficit, surplus_demand, surplus_generation, total_settlement
    """


    if conn is None:
        return pd.DataFrame()
    
    try:
        # First get the daily consumption and generation data and aggregate to monthly
        daily_query = """
            SELECT date,
                   SUM(allocated_generation) AS generation,
                   SUM(consumption) AS consumption,
                   SUM(deficit) AS deficit,
                   SUM(surplus_demand) AS surplus_demand,
                   SUM(surplus_generation) AS surplus_generation
            FROM settlement_data
            WHERE client_name = %s AND date BETWEEN %s AND %s
            GROUP BY date
            ORDER BY date;
        """
        df_daily = safe_read_sql(daily_query, conn, (client_name, start_date, end_date))
        
        if df_daily.empty:
            return pd.DataFrame()
        
        # Convert to monthly aggregation
        df_daily['date'] = pd.to_datetime(df_daily['date'])
        df_daily['month'] = df_daily['date'].dt.to_period('M').astype(str)
        
        df_monthly = (
            df_daily
            .groupby('month', as_index=False)[['generation', 'consumption', 'deficit', 'surplus_demand', 'surplus_generation']]
            .sum()
        )
        

        
        # Now get the banking settlement data
        # Note: banking_settlement.date is stored as 'YYYY-MM' format, so we need to extract year-month from start/end dates
        start_month = pd.to_datetime(start_date).strftime('%Y-%m')
        end_month = pd.to_datetime(end_date).strftime('%Y-%m')
        
        banking_query = """
            SELECT
                date AS month,
                SUM(matched_settled_sum) AS total_matched_settled_sum,
                SUM(intra_settlement) AS total_intra_settlement,
                SUM(inter_settlement) AS total_inter_settlement
            FROM
                banking_settlement
            WHERE
                client_name = %s AND date BETWEEN %s AND %s
            GROUP BY
                date
            ORDER BY
                date;
        """
        df_banking = safe_read_sql(banking_query, conn, (client_name, start_month, end_month))
        
        if not df_banking.empty:
            # Convert banking settlement month to same format
            df_banking['month'] = pd.to_datetime(df_banking['month']).dt.to_period('M').astype(str)
            
            # Calculate settlement components
            df_banking['settlement_with_banking'] = df_banking['total_intra_settlement'] + df_banking['total_inter_settlement']
            df_banking['total_settlement'] = df_banking['settlement_with_banking'] + df_banking['total_matched_settled_sum']
            
            # Merge with monthly consumption/generation data
            df_combined = pd.merge(
                df_monthly,
                df_banking[['month', 'total_settlement']],
                on='month',
                how='left'
            )
            
            # Fill NaN values with 0 for months without banking settlement
            df_combined['total_settlement'] = df_combined['total_settlement'].fillna(0)
        else:
            # If no banking settlement data, add zero column
            df_combined = df_monthly.copy()
            df_combined['total_settlement'] = 0
        
        # Convert month back to date for plotting
        df_combined['date'] = pd.to_datetime(df_combined['month'] + '-01')
        
        # Rename total_settlement to settled for compatibility with plotting function
        df_combined = df_combined.rename(columns={'total_settlement': 'settled'})


        
        return df_combined[['date', 'generation', 'consumption', 'deficit', 'surplus_demand', 'surplus_generation', 'settled']]
        
    except Exception as e:
        logging.error(f"Error in fetch_generation_consumption_with_banking_settlement: {e}")
        return pd.DataFrame()


