"""
Solar & Wind Energy Generation Dashboard
Main Streamlit Application
"""

import streamlit as st
from helper.setup_logger import setup_logger
import base64

# Import login components
from frontend.ui_components.login import (
    initialize_session_state,
    is_authenticated,
    login_form,
    add_logout_button
)

# Import dashboard components
from frontend.ui_components.dashboard_controls import (
    create_client_plant_filters,
    create_date_filters,
    setup_page,
    apply_custom_css,
    create_universal_plot_options
)

# Import display functions
from frontend.display_plots.summary_display import (
    display_generation_vs_consumption,
    display_generation_vs_consumption_hourly,
    display_unitwise_monthly_bill_analysis,
    display_tod_generation_consumption_lines
)
from frontend.display_plots.tod_display import (
    display_tod_generation_vs_consumption,
    display_tod_line_chart,
    display_tod_monthly_generation_consumption_charts
)

from frontend.display_plots.power_cost_display import (
    display_power_cost_analysis
)

# Import data management
from backend.data.db_data_manager import load_client_data
from db.db_setup import CONN

logging = setup_logger("app", "app.log")

def get_base64_image(image_path):
    """Convert image to base64 string"""
    try:
        with open(image_path, "rb") as img_file:
            return base64.b64encode(img_file.read()).decode()
    except Exception:
        return None

def main():
    """Main application function"""
    
    # Setup page configuration
    setup_page()
    
    # Initialize session state for authentication
    initialize_session_state()
    
    # Check if user is authenticated
    if not is_authenticated():
        login_form()
        return
    
    # Apply custom CSS for dashboard
    apply_custom_css()
    
    # Load client data
    with st.spinner("Loading client data..."):
        client_data = load_client_data()
    
    if not client_data:
        st.warning("📊 No client data found. Please contact support if this issue persists.")
        return
    
    # Create sidebar controls
    logo_base64 = get_base64_image("logo/logo_integrum.jpg")
    logo_html = f'<img src="data:image/jpeg;base64,{logo_base64}" style="height: 24px; width: 24px; vertical-align: middle; margin-right: 8px;"/>' if logo_base64 else '🎛️'
    
    st.sidebar.markdown(f"""
    <div style='background: linear-gradient(90deg, #1E88E5, #42A5F5); 
                padding: 1rem; margin: -1rem -1rem 1rem -1rem; 
                border-radius: 0.5rem;'>
        <h2 style='color: white; margin: 0; text-align: center;'>
            {logo_html}
            Dashboard Controls
        </h2>
    </div>
    """, unsafe_allow_html=True)
    
    st.sidebar.markdown("---")
    
    # Client and Plant Selection
    selected_client, selected_plant, _ = create_client_plant_filters(client_data)
    st.sidebar.markdown("---")
    
    # Date Selection
    start_date, end_date = create_date_filters()
    st.sidebar.markdown("---")
    
    # Universal Plot Options
    create_universal_plot_options()
    
    # Add logout button to sidebar
    add_logout_button()
        
    # Main content area
    if selected_client:
        # Create tabs
        tab1, tab2, tab3 = st.tabs(["Summary", "Generation and consumption", "Bill"])
        
        # Determine what to pass to display functions
        display_name = selected_plant if selected_plant else selected_client
            
        with tab1:
            if CONN is None:
                st.error("❌ Unable to connect to the database. Please contact support if this issue persists.")
                st.stop()
            
            st.subheader("Generation vs Consumption Last 12 Months")
            with st.spinner("Loading generation vs consumption data..."):
                display_generation_vs_consumption(display_name, start_date, end_date, is_hourly_aggregation=False)
            
            st.markdown("---")
            
            st.subheader("Generation vs Consumption Hourly")
            with st.spinner("Loading hourly generation vs consumption data..."):
                display_generation_vs_consumption_hourly(display_name, start_date, end_date)
            
            st.markdown("---")
            
            st.subheader("ToD 15m/60m/1d")
            with st.spinner("Loading ToD generation vs consumption line chart..."):
                display_tod_generation_consumption_lines(display_name, start_date, end_date)
            
            st.markdown("---")
            
            st.subheader("Unit-wise Monthly Bill Analysis")
            with st.spinner("Loading unit-wise monthly bill data..."):
                display_unitwise_monthly_bill_analysis(display_name, start_date, end_date, grid_rate=4.0, renewable_rate=2.0)

            
        with tab2:
            if CONN is None:
                st.error("❌ Unable to connect to the database. Please contact support if this issue persists.")
                st.stop()
            
            st.subheader("Monthly - Last 12 months TODwise.")
            with st.spinner("Loading monthly ToD data..."):
                display_tod_monthly_generation_consumption_charts(display_name)

            st.markdown("---")
            
            st.subheader("Monthly TOD")
            with st.spinner("Loading ToD comparison data..."):
                display_tod_generation_vs_consumption(display_name, start_date, end_date)
            
            st.markdown("---")
            
            st.subheader("Daily TOD")
            with st.spinner("Loading ToD line chart data..."):
                display_tod_line_chart(display_name, start_date, end_date)
                        
        with tab3:
            st.header("💰 Power Cost Analysis")
            display_power_cost_analysis(display_name)

        st.markdown("---")
    
    else:
        st.info("👋 Welcome! Please select a client from the sidebar to view your energy data.")

if __name__ == "__main__":
    main()