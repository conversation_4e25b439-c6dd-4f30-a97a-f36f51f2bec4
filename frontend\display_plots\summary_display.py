
import streamlit as st
from datetime import datetime, timedelta
from db.db_setup import CONN
from db.fetch_summary_data import (
    fetch_generation_consumption_data,
    fetch_unitwise_consumption_and_generation,
    fetch_unitwise_consumption_generation_with_location,
    fetch_generation_consumption_with_banking_settlement
)
from db.fetch_tod_tab_data import fetch_tod_data_with_granularity, fetch_combined_monthly_data
from visualizations.summary_tab_visual import (
    plot_generation_vs_consumption,
    plot_generation_vs_consumption_interactive,
    create_generation_only_plot, 
    create_consumption_plot,
    plot_consumption_and_generation_pie,
    plot_consumption_and_generation_pie_interactive,
    create_merged_consumption_generation_table,
    plot_generation_vs_consumption_hourly,
    plot_generation_vs_consumption_hourly_interactive,
    plot_tod_generation_consumption_lines
)
from visualizations.tod_config import SLOT_METADATA, normalize_slot_name
from visualizations.unit_wise_power_cost_calculations import (
    fetch_unitwise_monthly_data,
    calculate_unitwise_monthly_power_costs,
    summarize_unitwise_costs_table,
    summarize_unitwise_costs_by_consumption_unit,
    plot_unitwise_grid_vs_actual_cost_bar_chart,
    plot_unitwise_monthly_savings_heatmap,
    plot_single_unit_cost_timeseries,
    plot_single_unit_savings_trend
)
from frontend.ui_components.dashboard_controls import get_interactive_plot_setting


def convert_dates_to_string(start_date, end_date=None):
    """Convert date objects to string format."""
    if hasattr(start_date, 'strftime'):
        start_date_str = start_date.strftime('%Y-%m-%d')
    else:
        start_date_str = str(start_date)
    
    if end_date is None:
        end_date_str = start_date_str
    elif hasattr(end_date, 'strftime'):
        end_date_str = end_date.strftime('%Y-%m-%d')
    else:
        end_date_str = str(end_date)
    
    return start_date_str, end_date_str


def get_interactive_setting():
    """Get interactive plot setting with fallback."""
    try:
        return get_interactive_plot_setting()
    except:
        return False


def display_generation_vs_consumption(selected_plant, start_date, end_date=None, is_hourly_aggregation=False):
    """Display generation vs consumption chart with metrics."""
    
    # Set date range to last 12 months
    current_date = datetime.now()
    start_date = current_date - timedelta(days=365)
    end_date = current_date
    
    start_date_str = start_date.strftime('%Y-%m-%d')
    start_date_str = "2025-04-01"
    end_date_str = end_date.strftime('%Y-%m-%d')

    
    
    use_interactive = get_interactive_setting()
    
    # Fetch and display chart
    df = fetch_generation_consumption_with_banking_settlement(CONN, selected_plant, start_date_str, end_date_str)
    

    if df is not None and not df.empty:
        # Generate chart
        if use_interactive:
            fig = plot_generation_vs_consumption_interactive(
                df=df,
                plant_display_name=selected_plant,
                start_date=start_date_str,
                end_date=end_date_str,
                is_hourly_aggregation=is_hourly_aggregation
            )
            if fig:
                st.plotly_chart(fig, use_container_width=True)
            else:
                st.warning("⚠️ Unable to generate the interactive chart.")
        else:
            fig = plot_generation_vs_consumption(
                df=df,
                plant_display_name=selected_plant,
                start_date=start_date_str,
                end_date=end_date_str,
                is_hourly_aggregation=is_hourly_aggregation
            )
            if fig:
                st.pyplot(fig)
            else:
                st.warning("⚠️ Unable to generate the chart.")

        # Calculate and display metrics
        df_metrics = fetch_generation_consumption_data(CONN, selected_plant, start_date_str, end_date_str)
        
        if df_metrics is not None and not df_metrics.empty:
            # Calculate basic metrics
            total_generation_mwh = df_metrics['generation'].sum() / 1000
            total_consumption_mwh = df_metrics['consumption'].sum() / 1000
            total_settled_mwh = df_metrics['settled'].sum() / 1000
            surplus_demand_mwh = df_metrics['surplus_demand'].sum() / 1000
            
            # Calculate banking settlement metrics
            try:
                
                df_banking = fetch_combined_monthly_data(CONN, selected_plant)
                df_banking = df_banking[df_banking['month'] > '2025-03']

                if df_banking is not None and not df_banking.empty:
                    df_banking['settlement_with_banking'] = df_banking['total_intra_settlement'] + df_banking['total_inter_settlement']
                    df_banking['total_settlement'] = df_banking['settlement_with_banking'] + df_banking['total_matched_settled_sum']
                    df_banking['surplus_demand_after_banking'] = df_banking['surplus_demand_sum_after_inter'].clip(lower=0)
                    
                    # Calculate monthly replacement percentage and cap each month at 100%
                    df_banking['monthly_replacement_percentage'] = df_banking.apply(
                        lambda row: min(100.0, (row['total_settlement'] / row['total_consumption_sum'] * 100) 
                                       if row['total_consumption_sum'] > 0 else 0), axis=1
                    )
                    
                    # Calculate capped settlement values based on monthly caps
                    df_banking['capped_settlement'] = df_banking.apply(
                        lambda row: min(row['total_settlement'], row['total_consumption_sum']) 
                        if row['total_consumption_sum'] > 0 else row['total_settlement'], axis=1
                    )
                    
                    total_settlement_with_banking_mwh = df_banking['capped_settlement'].sum() / 1000
                    total_surplus_demand_after_banking_mwh = df_banking['surplus_demand_after_banking'].sum() / 1000
                    
                    replacement_percentage_with_banking = (
                        (total_settlement_with_banking_mwh / total_consumption_mwh) * 100 
                        if total_consumption_mwh > 0 else 0
                    )
                else:
                    replacement_percentage_with_banking = min(100.0, (total_settled_mwh / total_consumption_mwh * 100) if total_consumption_mwh > 0 else 0)
                    total_surplus_demand_after_banking_mwh = surplus_demand_mwh
            except:
                replacement_percentage_with_banking = min(100.0, (total_settled_mwh / total_consumption_mwh * 100) if total_consumption_mwh > 0 else 0)
                total_surplus_demand_after_banking_mwh = surplus_demand_mwh


            
            # Display metrics in columns
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("Total Generation (MWh)", f"{total_generation_mwh:.2f}")
            with col2:
                st.metric("Total Consumption (MWh)", f"{total_consumption_mwh:.2f}")
            with col3:
                st.metric("Replacement (with Banking) %", f"{replacement_percentage_with_banking:.2f}%")
            with col4:
                st.metric("Surplus Demand (Mwh)", f"{total_surplus_demand_after_banking_mwh:.2f}", help="Surplus demand after considering banking settlement")
        else:
            st.warning("⚠️ Metrics data is not available for the selected period.")
    else:
        st.warning("⚠️ No data available for the selected plant and date range.")



def display_generation_vs_consumption_hourly(selected_plant, start_date, end_date=None):
    """Display hourly generation vs consumption chart."""
    
    start_date_str, end_date_str = convert_dates_to_string(start_date, end_date)
    
    is_single_day = start_date_str == end_date_str
    use_hourly_aggregation = not is_single_day
    use_interactive = get_interactive_setting()
    
    df = fetch_generation_consumption_data(
        CONN, selected_plant, start_date_str, end_date_str, 
        hourly_aggregation=use_hourly_aggregation
    )

    if df is not None and not df.empty:
        if use_interactive:
            fig = plot_generation_vs_consumption_hourly_interactive(
                df=df,
                plant_display_name=selected_plant,
                start_date=start_date_str,
                end_date=end_date_str,
                is_hourly_aggregation=use_hourly_aggregation
            )
            if fig:
                st.plotly_chart(fig, use_container_width=True)
            else:
                st.warning("⚠️ Unable to generate the interactive hourly chart.")
        else:
            fig = plot_generation_vs_consumption_hourly(
                df=df,
                plant_display_name=selected_plant,
                start_date=start_date_str,
                end_date=end_date_str,
                is_hourly_aggregation=use_hourly_aggregation
            )
            if fig:
                st.pyplot(fig)
            else:
                st.warning("⚠️ Unable to generate the hourly chart.")
    else:
        st.warning("⚠️ No hourly data available for the selected plant and date range.")


    
def display_generation_only(selected_plant, start_date, end_date=None):
    """Display generation only chart."""
    
    start_date_str, end_date_str = convert_dates_to_string(start_date, end_date)
    
    df = fetch_generation_consumption_data(CONN, selected_plant, start_date_str, end_date_str)
    
    if df is not None and not df.empty:
        fig = create_generation_only_plot(
            df=df,
            plant_name=selected_plant,
            start_date=start_date_str,
            end_date=end_date_str
        )
        if fig:
            st.pyplot(fig)
        else:
            st.warning("⚠️ Unable to generate the generation chart.")
    else:
        st.warning("⚠️ No generation data available for the selected plant and date range.")




def display_consumption_only(selected_plant, start_date, end_date=None):
    """Display consumption only chart."""
    
    start_date_str, end_date_str = convert_dates_to_string(start_date, end_date)
    
    df = fetch_generation_consumption_data(CONN, selected_plant, start_date_str, end_date_str)
    
    if df is not None and not df.empty:
        fig = create_consumption_plot(
            df=df,
            plant_name=selected_plant,
            start_date=start_date_str,
            end_date=end_date_str
        )
        if fig:
            st.pyplot(fig)
        else:
            st.warning("⚠️ Unable to generate the consumption chart.")
    else:
        st.warning("⚠️ No consumption data available for the selected plant and date range.")







def display_consumption_and_generation_pie(selected_plant, start_date, end_date=None):
    """Display pie chart for consumption and generation breakdown by units."""
    
    start_date_str, end_date_str = convert_dates_to_string(start_date, end_date)
    use_interactive = get_interactive_setting()
    
    # Fetch data with location information
    df_with_location = fetch_unitwise_consumption_generation_with_location(
        CONN, selected_plant, start_date_str, end_date_str
    )

    if df_with_location is not None and not df_with_location.empty:
        # Fetch and display pie chart
        df_for_pie = fetch_unitwise_consumption_and_generation(
            CONN, selected_plant, start_date_str, end_date_str
        )
        
        if df_for_pie is not None and not df_for_pie.empty:
            if use_interactive:
                fig = plot_consumption_and_generation_pie_interactive(
                    df=df_for_pie,
                    plant_display_name=selected_plant,
                    start_date=start_date_str,
                    end_date=end_date_str
                )
                if fig:
                    st.plotly_chart(fig, use_container_width=True)
                else:
                    st.warning("⚠️ Unable to generate the interactive pie chart.")
            else:
                fig = plot_consumption_and_generation_pie(df_for_pie, selected_plant, start_date_str, end_date_str)
                if fig:
                    st.pyplot(fig)
                else:
                    st.warning("⚠️ Unable to generate the pie chart.")
        else:
            st.warning("⚠️ No pie chart data available for the selected period.")
        
        # Display merged table
        st.markdown("### 📋 Consumption & Generation summary by consumption unit")
        
        merged_table = create_merged_consumption_generation_table(df_with_location, selected_plant)
        
        if not merged_table.empty:
            st.dataframe(merged_table, use_container_width=True, hide_index=True)
        else:
            st.warning("⚠️ No summary table data available for the selected period.")
    else:
        st.warning("⚠️ No unitwise data available for the selected plant and date range.")


from st_aggrid import AgGrid, GridOptionsBuilder, GridUpdateMode


def display_unitwise_monthly_bill_analysis(selected_plant, start_date, end_date=None, grid_rate=4.0, renewable_rate=2.0):
    """Display unit-wise monthly bill analysis with grid power cost and after adjustment."""

    start_date_str, end_date_str = convert_dates_to_string(start_date, end_date)
    
    # Fetch and display analysis
    unitwise_monthly_data = fetch_unitwise_monthly_data(CONN, selected_plant)

    if not unitwise_monthly_data.empty:
        unitwise_costs = calculate_unitwise_monthly_power_costs(unitwise_monthly_data, grid_rate, renewable_rate)

        if not unitwise_costs.empty:
            # Get the selected view from the plots function and display table accordingly
            selected_view = display_unitwise_monthly_bill_plots(selected_plant, grid_rate, renewable_rate)
            
            # # Display summary table based on selection
            # st.subheader("📋 Unit-wise Cost Summary Table")
            
            # if selected_view == "All Units":
            #     # Show aggregated totals by consumption unit
            #     summary_table = summarize_unitwise_costs_by_consumption_unit(unitwise_costs)
            # else:
            #     # Show detailed monthly breakdown for the selected unit only
            #     filtered_costs = unitwise_costs[unitwise_costs['Unit'] == selected_view]
            #     summary_table = summarize_unitwise_costs_table(filtered_costs)
            
            # if not summary_table.empty:
            #     st.dataframe(summary_table, use_container_width=True, hide_index=True)
            # else:
            #     st.warning("⚠️ No summary data available.")

            
        else:
            st.warning("⚠️ No cost calculation data available for the selected period.")
    else:
        st.warning("⚠️ No unit-wise monthly data available for the selected period.")


def display_unitwise_monthly_bill_plots(selected_plant, grid_rate=4.0, renewable_rate=2.0):
    """Display unit-wise monthly bill analysis plots with dropdown for single unit view."""
    
    use_interactive = get_interactive_setting()
    
    # Fetch and calculate data
    unitwise_monthly_data = fetch_unitwise_monthly_data(CONN, selected_plant)
    
    if not unitwise_monthly_data.empty:
        unitwise_costs = calculate_unitwise_monthly_power_costs(unitwise_monthly_data, grid_rate, renewable_rate)
        
        if not unitwise_costs.empty:
            # Get unique units for dropdown
            available_units = sorted(unitwise_costs['Unit'].unique())
            
            # Unit selection dropdown
            view_options = ["All Units"] + available_units
            selected_view = st.selectbox(
                "Choose view type:",
                view_options,
                index=0,
                key="unit_view_selector"
            )
            
            if selected_view == "All Units":
                # Multi-unit view
                st.subheader("💰 Grid Cost vs Actual Cost - All Units")
                fig_bar = plot_unitwise_grid_vs_actual_cost_bar_chart(
                    df=unitwise_costs,
                    client_name=selected_plant,
                    use_interactive=use_interactive
                )
                
                if fig_bar:
                    if use_interactive:
                        st.plotly_chart(fig_bar, use_container_width=True)
                    else:
                        st.pyplot(fig_bar)
                else:
                    st.warning("⚠️ Unable to generate the bar chart.")
                
                st.markdown("---")
                
                # Monthly Savings Heatmap
                st.subheader("🔥 Monthly Savings Heatmap - All Units")
                fig_heatmap = plot_unitwise_monthly_savings_heatmap(
                    df=unitwise_costs,
                    client_name=selected_plant,
                    use_interactive=use_interactive
                )
                
                if fig_heatmap:
                    if use_interactive:
                        st.plotly_chart(fig_heatmap, use_container_width=True)
                    else:
                        st.pyplot(fig_heatmap)
                else:
                    st.warning("⚠️ Unable to generate the heatmap.")
            
            else:
                # Single unit view
                unit_data = unitwise_costs[unitwise_costs['Unit'] == selected_view]
                
                if not unit_data.empty:
                    # Cost trend time series
                    st.subheader(f"💰 Cost Trend Over Time - {selected_view}")
                    fig_timeseries = plot_single_unit_cost_timeseries(
                        df=unit_data,
                        unit_name=selected_view,
                        client_name=selected_plant,
                        use_interactive=use_interactive
                    )
                    
                    if fig_timeseries:
                        if use_interactive:
                            st.plotly_chart(fig_timeseries, use_container_width=True)
                        else:
                            st.pyplot(fig_timeseries)
                    else:
                        st.warning("⚠️ Unable to generate the time series chart.")
                    
                    st.markdown("---")
                    
                    # Savings trend
                    st.subheader(f"📈 Savings Trend - {selected_view}")
                    fig_savings_trend = plot_single_unit_savings_trend(
                        df=unit_data,
                        unit_name=selected_view,
                        client_name=selected_plant,
                        use_interactive=use_interactive
                    )
                    
                    if fig_savings_trend:
                        if use_interactive:
                            st.plotly_chart(fig_savings_trend, use_container_width=True)
                        else:
                            st.pyplot(fig_savings_trend)
                    else:
                        st.warning("⚠️ Unable to generate the savings trend chart.")
                else:
                    st.warning(f"⚠️ No data available for unit: {selected_view}")
            
            return selected_view
        else:
            st.warning("⚠️ No cost calculation data available for the selected period.")
            return "All Units"  # Default return value
    else:
        st.warning("⚠️ No unit-wise monthly data available for the selected period.")
        return "All Units"  # Default return value


def display_tod_generation_consumption_lines(selected_plant, start_date, end_date=None):
    """Display ToD-based generation and consumption line chart with granularity options."""
    
    start_date_str, end_date_str = convert_dates_to_string(start_date, end_date)
    use_interactive = get_interactive_setting()
    
    # Create controls
    col1, col2 = st.columns([1, 3])
    
    with col1:
        granularity = st.selectbox(
            "Select Granularity:",
            options=["daily", "60min", "15min"],
            index=0
        )
    
    with col2:
        st.write("Select TOD Slots:")
        all_slots = list(SLOT_METADATA.keys())
        
        # Create checkboxes for each slot
        slot_cols = st.columns(len(all_slots))
        selected_slots = []
        
        for i, slot in enumerate(all_slots):
            with slot_cols[i]:
                if st.checkbox(slot, value=True, key=f"tod_slot_{slot}"):
                    selected_slots.append(slot)
        
        # Default to all slots if none selected
        if not selected_slots:
            st.warning("⚠️ At least one TOD slot must be selected. Defaulting to all slots.")
            selected_slots = all_slots
    
    # Fetch and display data
    df = fetch_tod_data_with_granularity(
        CONN, selected_plant, start_date_str, end_date_str, granularity
    )
    
    if df is not None and not df.empty:
        # Normalize slot names and filter by selected slots
        if 'slot' in df.columns:
            df['slot'] = df['slot'].apply(normalize_slot_name)
            df = df[df['slot'].isin(selected_slots)]
        
        if not df.empty:
            # Create the line chart
            fig = plot_tod_generation_consumption_lines(
                df=df,
                plant_display_name=selected_plant,
                start_date=start_date_str,
                end_date=end_date_str,
                is_interactive=use_interactive
            )
            
            if fig:
                if use_interactive:
                    st.plotly_chart(fig, use_container_width=True)
                else:
                    st.pyplot(fig)
            else:
                st.warning("⚠️ Unable to generate the ToD line chart.")
        else:
            st.warning(f"⚠️ No data available for the selected TOD slots: {', '.join(selected_slots)}")
    else:
        st.warning(f"⚠️ No ToD data available for the selected plant and date range with {granularity} granularity.")

