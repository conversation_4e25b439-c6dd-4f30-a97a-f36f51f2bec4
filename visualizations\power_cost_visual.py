import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from matplotlib.ticker import FuncFormatter
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from helper.setup_logger import setup_logger

logging = setup_logger("power_cost_visual", "power_cost_visual.log")

def format_rupees_lakhs(x, _):
    """Format rupees with lakhs notation for large values"""
    try:
        if x >= 1e5:
            return f"₹{x / 1e5:.1f}L"
        else:
            return f"₹{x:.0f}"
    except (TypeError, ValueError) as e:
        logging.warning(f"Error formatting rupees value {x}: {str(e)}")
        return str(x) if x is not None else '₹0'
    except Exception as e:
        logging.error(f"Unexpected error in format_rupees_lakhs: {str(e)}")
        return '₹0'

def plot_costs_with_banking(df: pd.DataFrame, plant_name: str, cons_unit: str = None) -> plt.Figure:
    """
    Plot Grid Cost vs Actual Cost with Banking and Savings, formatted in Lakhs.
    """
    try:
        logging.info(f"Creating cost plot with banking for {plant_name}")
        
        if df is None or df.empty:
            logging.warning(f"No data available for cost plot with banking - {plant_name}")
            fig, ax = plt.subplots(figsize=(10, 6))
            ax.text(0.5, 0.5, "No cost data available for the selected period", ha='center', va='center', fontsize=14, color='red')
            unit_info = f" - {cons_unit}" if cons_unit else ""
            ax.set_title(f"Monthly Cost with Banking - {plant_name}{unit_info}", fontsize=14, fontweight='bold')
            return fig
        
        # Validate required columns
        required_columns = ['Date', 'Grid Cost (₹)', 'Actual Cost (₹)', 'Savings (₹)']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logging.error(f"Missing required columns in cost data: {missing_columns}")
            fig, ax = plt.subplots(figsize=(10, 6))
            ax.text(0.5, 0.5, "Data structure error - please contact support", ha='center', va='center', fontsize=14, color='red')
            unit_info = f" - {cons_unit}" if cons_unit else ""
            ax.set_title(f"Monthly Cost with Banking - {plant_name}{unit_info}", fontsize=14, fontweight='bold')
            return fig
        
        df = df.copy()
        df['month'] = pd.to_datetime(df['Date'] + '-01')
        df = df.sort_values('month')
        df['month_str'] = df['month'].dt.strftime('%b %Y')
        x = np.arange(len(df))

        fig, ax = plt.subplots(figsize=(10, 6))
        
        logging.info(f"Successfully prepared cost data for {plant_name}")
        
    except pd.errors.OutOfBoundsDatetime as e:
        logging.error(f"Date parsing error for cost plot {plant_name}: {str(e)}")
        fig, ax = plt.subplots(figsize=(10, 6))
        ax.text(0.5, 0.5, "Date format error - please check your date range", ha='center', va='center', fontsize=14, color='red')
        ax.set_title(f"Monthly Cost with Banking - {plant_name}", fontsize=14, fontweight='bold')
        return fig
    except Exception as e:
        logging.error(f"Error initializing cost plot with banking for {plant_name}: {str(e)}")
        fig, ax = plt.subplots(figsize=(10, 6))
        ax.text(0.5, 0.5, "Error loading chart - please try again", ha='center', va='center', fontsize=14, color='red')
        ax.set_title(f"Monthly Cost with Banking - {plant_name}", fontsize=14, fontweight='bold')
        return fig

    try:
        # Main Lines
        ax.plot(x, df['Grid Cost (₹)'], label='Grid Cost', marker='o', linewidth=2.5, color='#1E88E5')
        ax.plot(x, df['Actual Cost (₹)'], label='Actual Cost', marker='s', linestyle='--', linewidth=2.5, color='#43A047')
        ax.plot(x, df['Savings (₹)'], label='Savings', marker='^', linestyle=':', linewidth=2.5, color='#E53935')  # New savings line

        # Annotation for Savings
        for i, val in enumerate(df['Savings (₹)']):
            if not pd.isna(val) and val > 0:
                ax.text(
                    x[i],
                    max(df['Grid Cost (₹)'].iloc[i], df['Actual Cost (₹)'].iloc[i]) * 1.02,
                    f"₹{val / 1e5:.2f}L",
                    ha='center',
                    fontsize=9,
                    color='gray'
                )

        unit_info = f" - {cons_unit}" if cons_unit else ""
        ax.set_title(f"Monthly Cost with Banking\n{plant_name}{unit_info}", fontsize=14)
        ax.set_xlabel("Month")
        ax.set_ylabel("Cost (₹ in Lakhs)")
        ax.set_xticks(x)
        ax.set_xticklabels(df['month_str'], rotation=45, ha='right')
        ax.yaxis.set_major_formatter(FuncFormatter(format_rupees_lakhs))
        ax.grid(True, linestyle='--', alpha=0.6)
        ax.legend(loc='upper right', frameon=False)

        plt.tight_layout()
        logging.info(f"Successfully created cost plot with banking for {plant_name}")
        return fig
        
    except KeyError as e:
        logging.error(f"Missing required column for cost plot {plant_name}: {str(e)}")
        ax.text(0.5, 0.5, "Data column missing - please contact support", ha='center', va='center', fontsize=14, color='red')
        ax.set_title(f"Monthly Cost with Banking - {plant_name}", fontsize=14, fontweight='bold')
        return fig
    except Exception as e:
        logging.error(f"Error creating cost plot with banking for {plant_name}: {str(e)}")
        ax.text(0.5, 0.5, "Chart creation error - please try again", ha='center', va='center', fontsize=14, color='red')
        ax.set_title(f"Monthly Cost with Banking - {plant_name}", fontsize=14, fontweight='bold')
        return fig




def plot_costs_without_banking(df: pd.DataFrame, plant_name: str, cons_unit: str = None) -> plt.Figure:
    """
    Plot Grid Cost vs Actual Cost (without banking logic), now includes Savings line.
    """
    try:
        logging.info(f"Creating cost plot without banking for {plant_name}")
        
        if df is None or df.empty:
            logging.warning(f"No data available for cost plot without banking - {plant_name}")
            fig, ax = plt.subplots(figsize=(10, 6))
            ax.text(0.5, 0.5, "No cost data available for the selected period", ha='center', va='center', fontsize=14, color='red')
            ax.set_title(f"Monthly Cost without Banking - {plant_name}", fontsize=14, fontweight='bold')
            return fig
        
        # Validate required columns
        required_columns = ['Date', 'Grid Cost (₹)', 'Actual Cost (₹)', 'Savings (₹)']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logging.error(f"Missing required columns in cost data (without banking): {missing_columns}")
            fig, ax = plt.subplots(figsize=(10, 6))
            ax.text(0.5, 0.5, "Data structure error - please contact support", ha='center', va='center', fontsize=14, color='red')
            ax.set_title(f"Monthly Cost without Banking - {plant_name}", fontsize=14, fontweight='bold')
            return fig
        
        df = df.copy()
        df['month'] = pd.to_datetime(df['Date'] + '-01')
        df = df.sort_values('month')
        df['month_str'] = df['month'].dt.strftime('%b %Y')
        x = np.arange(len(df))

        fig, ax = plt.subplots(figsize=(10, 6))
        
        logging.info(f"Successfully prepared cost data without banking for {plant_name}")
        
    except pd.errors.OutOfBoundsDatetime as e:
        logging.error(f"Date parsing error for cost plot without banking {plant_name}: {str(e)}")
        fig, ax = plt.subplots(figsize=(10, 6))
        ax.text(0.5, 0.5, "Date format error - please check your date range", ha='center', va='center', fontsize=14, color='red')
        ax.set_title(f"Monthly Cost without Banking - {plant_name}", fontsize=14, fontweight='bold')
        return fig
    except Exception as e:
        logging.error(f"Error initializing cost plot without banking for {plant_name}: {str(e)}")
        fig, ax = plt.subplots(figsize=(10, 6))
        ax.text(0.5, 0.5, "Error loading chart - please try again", ha='center', va='center', fontsize=14, color='red')
        ax.set_title(f"Monthly Cost without Banking - {plant_name}", fontsize=14, fontweight='bold')
        return fig

    try:
        # Core lines
        ax.plot(x, df['Grid Cost (₹)'], label='Grid Cost', marker='o', linewidth=2.5, color='#1E88E5')
        ax.plot(x, df['Actual Cost (₹)'], label='Actual Cost', marker='s', linestyle='--', linewidth=2.5, color='#E53935')
        ax.plot(x, df['Savings (₹)'], label='Savings', marker='^', linestyle=':', linewidth=2.5, color='#43A047')  # New line

        # Annotate savings
        for i, val in enumerate(df['Savings (₹)']):
            if not pd.isna(val) and val > 0:
                ax.text(
                    x[i],
                    max(df['Grid Cost (₹)'].iloc[i], df['Actual Cost (₹)'].iloc[i]) * 1.02,
                    f"₹{val / 1e5:.2f}L",
                    ha='center',
                    fontsize=9,
                    color='gray'
                )

        ax.set_title(f"Monthly Cost without Banking\n{plant_name}", fontsize=14)
        ax.set_xlabel("Month")
        ax.set_ylabel("Cost (₹ in Lakhs)")
        ax.set_xticks(x)
        ax.set_xticklabels(df['month_str'], rotation=45, ha='right')
        ax.yaxis.set_major_formatter(FuncFormatter(format_rupees_lakhs))
        ax.grid(True, linestyle='--', alpha=0.6)
        ax.legend(loc='upper right', frameon=False)

        plt.tight_layout()
        logging.info(f"Successfully created cost plot without banking for {plant_name}")
        return fig
        
    except KeyError as e:
        logging.error(f"Missing required column for cost plot without banking {plant_name}: {str(e)}")
        ax.text(0.5, 0.5, "Data column missing - please contact support", ha='center', va='center', fontsize=14, color='red')
        ax.set_title(f"Monthly Cost without Banking - {plant_name}", fontsize=14, fontweight='bold')
        return fig
    except Exception as e:
        logging.error(f"Error creating cost plot without banking for {plant_name}: {str(e)}")
        ax.text(0.5, 0.5, "Chart creation error - please try again", ha='center', va='center', fontsize=14, color='red')
        ax.set_title(f"Monthly Cost without Banking - {plant_name}", fontsize=14, fontweight='bold')
        return fig


def plot_costs_with_banking_interactive(df: pd.DataFrame, plant_name: str):
    """
    Interactive Plotly version of cost analysis with banking and savings.
    """
    try:
        logging.info(f"Creating interactive cost plot with banking for {plant_name}")
        
        if df is None or df.empty:
            logging.warning(f"No data available for interactive cost plot with banking - {plant_name}")
            # Create empty figure with message
            fig = go.Figure()
            fig.add_annotation(
                text="No cost data available for the selected period",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                font=dict(size=20, color="red")
            )
            fig.update_layout(
                title=f"Monthly Cost with Banking - {plant_name}",
                xaxis=dict(showgrid=False, showticklabels=False),
                yaxis=dict(showgrid=False, showticklabels=False)
            )
            return fig
        
        # Validate required columns
        required_columns = ['Date', 'Grid Cost (₹)', 'Actual Cost (₹)', 'Savings (₹)']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logging.error(f"Missing required columns in interactive cost data: {missing_columns}")
            fig = go.Figure()
            fig.add_annotation(
                text="Data structure error - please contact support",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                font=dict(size=20, color="red")
            )
            fig.update_layout(
                title=f"Monthly Cost with Banking - {plant_name}",
                xaxis=dict(showgrid=False, showticklabels=False),
                yaxis=dict(showgrid=False, showticklabels=False)
            )
            return fig
        
    except Exception as e:
        logging.error(f"Error initializing interactive cost plot with banking for {plant_name}: {str(e)}")
        fig = go.Figure()
        fig.add_annotation(
            text="Error loading chart - please try again",
            xref="paper", yref="paper",
            x=0.5, y=0.5, xanchor='center', yanchor='middle',
            font=dict(size=20, color="red")
        )
        fig.update_layout(
            title=f"Monthly Cost with Banking - {plant_name}",
            xaxis=dict(showgrid=False, showticklabels=False),
            yaxis=dict(showgrid=False, showticklabels=False)
        )
        return fig
    
    df = df.copy()
    df['month'] = pd.to_datetime(df['Date'] + '-01')
    df = df.sort_values('month')
    df['month_str'] = df['month'].dt.strftime('%b %Y')
    
    # Create figure
    fig = go.Figure()
    
    # Helper function to format currency
    def format_currency(value):
        if value >= 1e5:
            return f"₹{value / 1e5:.1f}L"
        else:
            return f"₹{value:,.0f}"
    
    # Grid Cost Line
    fig.add_trace(go.Scatter(
        x=df['month_str'],
        y=df['Grid Cost (₹)'],
        mode='lines+markers',
        name='Grid Cost',
        line=dict(color='#1E88E5', width=3),
        marker=dict(size=8, symbol='circle'),
        hovertemplate='<b>📅 Month:</b> %{x}<br>' +
                     '<b>⚡ Grid Cost:</b> %{customdata[0]}<br>' +
                     '<b>💰 Amount:</b> ₹%{y:,.0f}<br>' +
                     '<extra></extra>',
        customdata=[[format_currency(val)] for val in df['Grid Cost (₹)']]
    ))
    
    # Actual Cost Line
    fig.add_trace(go.Scatter(
        x=df['month_str'],
        y=df['Actual Cost (₹)'],
        mode='lines+markers',
        name='Actual Cost',
        line=dict(color='#43A047', width=3, dash='dash'),
        marker=dict(size=8, symbol='square'),
        hovertemplate='<b>📅 Month:</b> %{x}<br>' +
                     '<b>🏦 Actual Cost:</b> %{customdata[0]}<br>' +
                     '<b>💰 Amount:</b> ₹%{y:,.0f}<br>' +
                     '<extra></extra>',
        customdata=[[format_currency(val)] for val in df['Actual Cost (₹)']]
    ))
    
    # Savings Line
    fig.add_trace(go.Scatter(
        x=df['month_str'],
        y=df['Savings (₹)'],
        mode='lines+markers',
        name='Savings',
        line=dict(color='#E53935', width=3, dash='dot'),
        marker=dict(size=8, symbol='triangle-up'),
        hovertemplate='<b>📅 Month:</b> %{x}<br>' +
                     '<b>💸 Savings:</b> %{customdata[0]}<br>' +
                     '<b>💰 Amount:</b> ₹%{y:,.0f}<br>' +
                     '<b>📊 Percentage:</b> %{customdata[1]}<br>' +
                     '<extra></extra>',
        customdata=[[format_currency(val), 
                    f"{(val / df['Grid Cost (₹)'].iloc[i] * 100):.1f}%" if df['Grid Cost (₹)'].iloc[i] > 0 else "N/A"]
                   for i, val in enumerate(df['Savings (₹)'])]
    ))
    
    # Add savings annotations for significant values
    for i, val in enumerate(df['Savings (₹)']):
        if not pd.isna(val) and val > 0:
            max_cost = max(df['Grid Cost (₹)'].iloc[i], df['Actual Cost (₹)'].iloc[i])
            fig.add_annotation(
                x=df['month_str'].iloc[i],
                y=max_cost * 1.05,
                text=f"₹{val / 1e5:.2f}L",
                showarrow=False,
                font=dict(size=10, color='gray'),
                bgcolor='rgba(255,255,255,0.8)',
                bordercolor='gray',
                borderwidth=1
            )
    
    # Update layout
    fig.update_layout(
        title=dict(
            text=f"Monthly Cost with Banking<br>{plant_name}",
            x=0.5,
            xanchor='center',
            font=dict(size=16)
        ),
        xaxis=dict(
            title="Month",
            tickangle=45,
            showgrid=True,
            gridcolor='rgba(128,128,128,0.2)'
        ),
        yaxis=dict(
            title="Cost (₹ in Lakhs)",
            tickformat=',.0f',
            showgrid=True,
            gridcolor='rgba(128,128,128,0.2)'
        ),
        height=600,
        showlegend=True,
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        ),
        hovermode='closest'
    )
    
    try:
        logging.info(f"Successfully created interactive cost plot with banking for {plant_name}")
        return fig
    except Exception as e:
        logging.error(f"Error finalizing interactive cost plot with banking for {plant_name}: {str(e)}")
        return fig


def plot_costs_without_banking_interactive(df: pd.DataFrame, plant_name: str):
    """
    Interactive Plotly version of cost analysis without banking logic.
    """
    try:
        logging.info(f"Creating interactive cost plot without banking for {plant_name}")
        
        if df is None or df.empty:
            logging.warning(f"No data available for interactive cost plot without banking - {plant_name}")
            # Create empty figure with message
            fig = go.Figure()
            fig.add_annotation(
                text="No cost data available for the selected period",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                font=dict(size=20, color="red")
            )
            fig.update_layout(
                title=f"Monthly Cost without Banking - {plant_name}",
                xaxis=dict(showgrid=False, showticklabels=False),
                yaxis=dict(showgrid=False, showticklabels=False)
            )
            return fig
        
        # Validate required columns
        required_columns = ['Date', 'Grid Cost (₹)', 'Actual Cost (₹)', 'Savings (₹)']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logging.error(f"Missing required columns in interactive cost data without banking: {missing_columns}")
            fig = go.Figure()
            fig.add_annotation(
                text="Data structure error - please contact support",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                font=dict(size=20, color="red")
            )
            fig.update_layout(
                title=f"Monthly Cost without Banking - {plant_name}",
                xaxis=dict(showgrid=False, showticklabels=False),
                yaxis=dict(showgrid=False, showticklabels=False)
            )
            return fig
        
    except Exception as e:
        logging.error(f"Error initializing interactive cost plot without banking for {plant_name}: {str(e)}")
        fig = go.Figure()
        fig.add_annotation(
            text="Error loading chart - please try again",
            xref="paper", yref="paper",
            x=0.5, y=0.5, xanchor='center', yanchor='middle',
            font=dict(size=20, color="red")
        )
        fig.update_layout(
            title=f"Monthly Cost without Banking - {plant_name}",
            xaxis=dict(showgrid=False, showticklabels=False),
            yaxis=dict(showgrid=False, showticklabels=False)
        )
        return fig
    
    df = df.copy()
    df['month'] = pd.to_datetime(df['Date'] + '-01')
    df = df.sort_values('month')
    df['month_str'] = df['month'].dt.strftime('%b %Y')
    
    # Create figure
    fig = go.Figure()
    
    # Helper function to format currency
    def format_currency(value):
        if value >= 1e5:
            return f"₹{value / 1e5:.1f}L"
        else:
            return f"₹{value:,.0f}"
    
    # Grid Cost Line
    fig.add_trace(go.Scatter(
        x=df['month_str'],
        y=df['Grid Cost (₹)'],
        mode='lines+markers',
        name='Grid Cost',
        line=dict(color='#1E88E5', width=3),
        marker=dict(size=8, symbol='circle'),
        hovertemplate='<b>📅 Month:</b> %{x}<br>' +
                     '<b>⚡ Grid Cost:</b> %{customdata[0]}<br>' +
                     '<b>💰 Amount:</b> ₹%{y:,.0f}<br>' +
                     '<extra></extra>',
        customdata=[[format_currency(val)] for val in df['Grid Cost (₹)']]
    ))
    
    # Actual Cost Line
    fig.add_trace(go.Scatter(
        x=df['month_str'],
        y=df['Actual Cost (₹)'],
        mode='lines+markers',
        name='Actual Cost',
        line=dict(color='#E53935', width=3, dash='dash'),
        marker=dict(size=8, symbol='square'),
        hovertemplate='<b>📅 Month:</b> %{x}<br>' +
                     '<b>🏦 Actual Cost:</b> %{customdata[0]}<br>' +
                     '<b>💰 Amount:</b> ₹%{y:,.0f}<br>' +
                     '<extra></extra>',
        customdata=[[format_currency(val)] for val in df['Actual Cost (₹)']]
    ))
    
    # Savings Line
    fig.add_trace(go.Scatter(
        x=df['month_str'],
        y=df['Savings (₹)'],
        mode='lines+markers',
        name='Savings',
        line=dict(color='#43A047', width=3, dash='dot'),
        marker=dict(size=8, symbol='triangle-up'),
        hovertemplate='<b>📅 Month:</b> %{x}<br>' +
                     '<b>💸 Savings:</b> %{customdata[0]}<br>' +
                     '<b>💰 Amount:</b> ₹%{y:,.0f}<br>' +
                     '<b>📊 Percentage:</b> %{customdata[1]}<br>' +
                     '<extra></extra>',
        customdata=[[format_currency(val), 
                    f"{(val / df['Grid Cost (₹)'].iloc[i] * 100):.1f}%" if df['Grid Cost (₹)'].iloc[i] > 0 else "N/A"]
                   for i, val in enumerate(df['Savings (₹)'])]
    ))
    
    # Add savings annotations for significant values
    for i, val in enumerate(df['Savings (₹)']):
        if not pd.isna(val) and val > 0:
            max_cost = max(df['Grid Cost (₹)'].iloc[i], df['Actual Cost (₹)'].iloc[i])
            fig.add_annotation(
                x=df['month_str'].iloc[i],
                y=max_cost * 1.05,
                text=f"₹{val / 1e5:.2f}L",
                showarrow=False,
                font=dict(size=10, color='gray'),
                bgcolor='rgba(255,255,255,0.8)',
                bordercolor='gray',
                borderwidth=1
            )
    
    # Update layout
    fig.update_layout(
        title=dict(
            text=f"Monthly Cost without Banking<br>{plant_name}",
            x=0.5,
            xanchor='center',
            font=dict(size=16)
        ),
        xaxis=dict(
            title="Month",
            tickangle=45,
            showgrid=True,
            gridcolor='rgba(128,128,128,0.2)'
        ),
        yaxis=dict(
            title="Cost (₹ in Lakhs)",
            tickformat=',.0f',
            showgrid=True,
            gridcolor='rgba(128,128,128,0.2)'
        ),
        height=600,
        showlegend=True,
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        ),
        hovermode='closest'
    )
    
    try:
        logging.info(f"Successfully created interactive cost plot without banking for {plant_name}")
        return fig
    except Exception as e:
        logging.error(f"Error finalizing interactive cost plot without banking for {plant_name}: {str(e)}")
        return fig