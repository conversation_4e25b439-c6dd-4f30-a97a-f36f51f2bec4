import streamlit as st
import pandas as pd
from db.fetch_tod_tab_data import fetch_combined_monthly_data, fetch_available_cons_units
from db.db_setup import CONN
from visualizations.power_cost_calculations import (
    calculate_monthly_power_costs, 
    summarize_costs_table, 
    calculate_monthly_costs_without_banking, 
    summarize_costs_table_without_banking
)
from visualizations.power_cost_visual import (
    plot_costs_with_banking, 
    plot_costs_without_banking,
    plot_costs_with_banking_interactive,
    plot_costs_without_banking_interactive
)
from frontend.ui_components.dashboard_controls import get_interactive_plot_setting

def display_power_cost_analysis(selected_plant):
    """Display power cost analysis with banking and without banking options."""
    
    # Initialize session state keys for this plant
    banking_key = f"power_cost_banking_option_{selected_plant}"
    cons_unit_key = f"power_cost_cons_unit_{selected_plant}"
    grid_rate_key = f"power_cost_grid_rate_{selected_plant}"
    renewable_rate_key = f"power_cost_renewable_rate_{selected_plant}"
    
    # Get universal plot options setting
    try:
        use_interactive = get_interactive_plot_setting()
    except Exception:
        use_interactive = False
    
    # Get available cons_units for the selected plant
    try:
        available_cons_units = fetch_available_cons_units(CONN, selected_plant)
    except Exception:
        available_cons_units = []

    # Initialize session state variables with default values
    if banking_key not in st.session_state:
        st.session_state[banking_key] = "Without Banking"
    if cons_unit_key not in st.session_state:
        st.session_state[cons_unit_key] = "All Units"
    if grid_rate_key not in st.session_state:
        st.session_state[grid_rate_key] = 4.0
    if renewable_rate_key not in st.session_state:
        st.session_state[renewable_rate_key] = 2.0

    # Power cost input section with vertical layout for controls
    col_left, col_right = st.columns([2, 1])

    with col_left:
        # Add radio button for banking option selection
        banking_option = st.radio(
            "Analysis Type:",
            options=["Without Banking", "With Banking"],
            index=0 if st.session_state[banking_key] == "Without Banking" else 1,
            horizontal=True,
            key=banking_key,
            help="Choose whether to include banking in the power cost analysis"
        )
        
        # Prepare cons_unit options
        cons_unit_options = ["All Units"] + available_cons_units
        
        # Ensure current session state value is still valid
        if st.session_state[cons_unit_key] not in cons_unit_options:
            st.session_state[cons_unit_key] = "All Units"
        
        # Add dropdown for cons_unit selection
        selected_cons_unit = st.selectbox(
            "Select Consumption Unit:",
            options=cons_unit_options,
            index=cons_unit_options.index(st.session_state[cons_unit_key]),
            key=cons_unit_key,
            help="Choose a specific consumption unit or 'All Units' for combined analysis"
        )

    with col_right:
        # Grid power cost input
        grid_rate = st.number_input(
            "Grid Cost (₹/kWh)",
            min_value=0.01,
            max_value=50.0,
            value=st.session_state[grid_rate_key],
            step=0.1,
            key=grid_rate_key,
            help="Enter grid electricity cost per kWh"
        )

        # Renewable power cost input
        renewable_rate = st.number_input(
            "Renewable Cost (₹/kWh)",
            min_value=0.01,
            max_value=50.0,
            value=st.session_state[renewable_rate_key],
            step=0.1,
            key=renewable_rate_key,
            help="Enter renewable electricity cost per kWh"
        )

    # Validate inputs and fetch data
    if grid_rate is None or grid_rate <= 0:
        st.error("Please enter a valid grid cost value greater than 0")
        return

    if renewable_rate is None or renewable_rate <= 0:
        st.error("Please enter a valid renewable cost value greater than 0")
        return
        
    # Determine cons_unit parameter for data fetching
    cons_unit_param = None if selected_cons_unit == "All Units" else selected_cons_unit
    
    # Fetch data
    main_df = fetch_combined_monthly_data(CONN, selected_plant, cons_unit_param)
    
    if main_df is None or main_df.empty:
        st.warning("⚠️ No data available for the selected plant. Please try selecting a different plant or check if data exists for this plant.")
        return
    
    # Display content based on selected banking option
    if banking_option == "With Banking":
        # Calculate costs with banking
        df_calculated = calculate_monthly_power_costs(main_df, grid_rate, renewable_rate)
        
        if df_calculated is None or df_calculated.empty:
            st.warning("⚠️ Unable to calculate power costs with banking for the current data. Please check if sufficient data is available.")
            return

        # Get summary data
        summary = summarize_costs_table(df_calculated)
        
        unit_info = f" - {selected_cons_unit}" if selected_cons_unit != "All Units" else ""
        st.subheader(f"With Banking{unit_info}")
        
        # Display metrics
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric(
                "Total Grid Cost",
                f"₹{summary.iloc[0]['Total Grid Cost (₹)']/100000:.2f}L",
                help="Total cost if all energy was purchased from grid"
            )

        with col2:
            st.metric(
                "Actual Cost",
                f"₹{summary.iloc[0]['Total Actual Cost (₹)']/100000:.2f}L",
                help="Actual cost after solar/wind generation offset"
            )

        with col3:
            st.metric(
                "Total Savings",
                f"₹{summary.iloc[0]['Total Savings (₹)']/100000:.2f}L",
                delta=f"{summary.iloc[0]['Savings (%)']:.1f}%",
                help="Total money saved due to renewable generation"
            )

        with col4:
            st.metric(
                "Energy Offset",
                f"{summary.iloc[0]['Energy Offset']:.0f} kWh",
                help="Total energy offset by renewable generation"
            )

        # Plot chart
        if use_interactive:
            fig = plot_costs_with_banking_interactive(df_calculated, selected_plant)
            if fig:
                st.plotly_chart(fig, use_container_width=True)
        else:
            fig = plot_costs_with_banking(df_calculated, selected_plant)
            if fig:
                st.pyplot(fig)
        
        # Display the detailed table
        st.subheader(f"Monthly Power Cost Analysis (With Banking{unit_info})")
        
        # Convert monetary columns to lakhs for display
        df_display = df_calculated.copy()
        monetary_columns = [col for col in df_display.columns if 
                          any(keyword in col.lower() for keyword in ['cost', '₹', 'rupee']) and 
                          '%' not in col.lower() and 'percentage' not in col.lower()]
        
        for col in monetary_columns:
            if df_display[col].dtype in ['float64', 'int64']:
                df_display[col] = df_display[col].apply(lambda x: f"₹{x/100000:.2f}L" if pd.notna(x) and x != 0 else "₹0.00L")
        
        st.dataframe(df_display, use_container_width=True)
    
    else:  # Without Banking
        # Calculate costs without banking
        df_calculated_without_banking = calculate_monthly_costs_without_banking(main_df, grid_rate, renewable_rate)
        
        if df_calculated_without_banking is None or df_calculated_without_banking.empty:
            st.warning("⚠️ Unable to calculate power costs without banking for the current data. Please check if sufficient data is available.")
            return
        
        # Get summary data without banking
        summary_without_banking = summarize_costs_table_without_banking(df_calculated_without_banking)
        
        unit_info = f" - {selected_cons_unit}" if selected_cons_unit != "All Units" else ""
        st.subheader(f"Without Banking{unit_info}")
        
        # Display metrics for Without Banking
        col1_nb, col2_nb, col3_nb, col4_nb = st.columns(4)
        
        with col1_nb:
            st.metric(
                "Total Grid Cost",
                f"₹{summary_without_banking.iloc[0]['Total Grid Cost (₹)']/100000:.2f}L",
                help="Total cost if all energy was purchased from grid"
            )

        with col2_nb:
            st.metric(
                "Actual Cost",
                f"₹{summary_without_banking.iloc[0]['Total Actual Cost (₹)']/100000:.2f}L",
                help="Actual cost after solar/wind generation offset"
            )

        with col3_nb:
            st.metric(
                "Total Savings",
                f"₹{summary_without_banking.iloc[0]['Total Savings (₹)']/100000:.2f}L",
                delta=f"{summary_without_banking.iloc[0]['Average Savings (%)']:.1f}%",
                help="Total money saved due to renewable generation"
            )

        with col4_nb:
            st.metric(
                "Energy Offset",
                f"{summary_without_banking.iloc[0]['Energy Offset']:.0f} kWh",
                help="Total energy offset by renewable generation"
            )

        # Plot chart
        if use_interactive:
            fig = plot_costs_without_banking_interactive(df_calculated_without_banking, selected_plant)
            if fig:
                st.plotly_chart(fig, use_container_width=True)
        else:
            fig = plot_costs_without_banking(df_calculated_without_banking, selected_plant)
            if fig:
                st.pyplot(fig)
        
        # Display the detailed table without banking
        st.subheader(f"Monthly Power Cost Analysis (Without Banking{unit_info})")
        
        # Convert monetary columns to lakhs for display
        df_display_without_banking = df_calculated_without_banking.copy()
        monetary_columns_nb = [col for col in df_display_without_banking.columns if 
                             any(keyword in col.lower() for keyword in ['cost', '₹', 'rupee']) and 
                             '%' not in col.lower() and 'percentage' not in col.lower()]
        
        for col in monetary_columns_nb:
            if df_display_without_banking[col].dtype in ['float64', 'int64']:
                df_display_without_banking[col] = df_display_without_banking[col].apply(lambda x: f"₹{x/100000:.2f}L" if pd.notna(x) and x != 0 else "₹0.00L")
        
        st.dataframe(df_display_without_banking, use_container_width=True)


