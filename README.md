# Solar & Wind Energy Generation Dashboard

A comprehensive Streamlit-based dashboard for monitoring and analyzing solar and wind energy generation, consumption patterns, and settlement data.

## Features

### 🔐 Authentication System
- **User Login**: Secure login system with session management
- **Session State**: Persistent authentication across page refreshes
- **Logout Functionality**: Clean logout with session cleanup

### 🎛️ Dashboard Controls
- **Client Selection**: Dynamic client dropdown with database integration
- **Plant Selection**: Smart solar/wind plant selection with mutual exclusion logic
- **Date Filtering**: Flexible date range selection with single-day and multi-day analysis
- **Current Selection Display**: Visual confirmation of active selections with reset functionality
- **Universal Plot Options**: Configurable plot settings across all visualizations

### 📊 Analysis Tabs
- **Summary Tab**: Generation vs consumption analysis, hourly data, ToD lines, and unit-wise monthly bill analysis
- **Generation and Consumption Tab**: Time-based analysis with monthly/daily ToD views and settlement heatmaps
- **Bill Tab**: Comprehensive power cost analysis with financial breakdowns

### 🎨 User Interface
- Modern, responsive design with custom CSS styling
- Color-coded plant type indicators (Solar: Amber, Wind: Blue, Combined: Green)
- Loading states and error handling with spinner indicators
- Interactive buttons and controls
- Custom logo integration and branding
- Sidebar navigation with organized controls

## Installation

### Prerequisites
- Python 3.8 or higher
- MySQL database with energy data
- Required Python packages (see requirements.txt)

### Setup Steps

1. **Clone or download the project**
   ```bash
   cd "c:/Users/<USER>/OneDrive/Desktop/New Dashboard punnet/integrum-dashboard"
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure database connection**
   - Update database credentials in `db/db_setup.py`
   - Ensure your MySQL database is running and accessible

4. **Run the dashboard**
   ```bash
   streamlit run app.py
   ```
   
   Or use the provided batch file (Windows):
   ```bash
   run_dashboard.bat
   ```

5. **Access the dashboard**
   - Open your web browser
   - Navigate to `http://localhost:8501`

## Project Structure

```
integrum-dashboard/
├── app.py                          # Main Streamlit application
├── requirements.txt                # Python dependencies
├── README.md                      # This file
├── run_dashboard.bat              # Windows batch launcher
│
├── frontend/                      # Frontend components
│   ├── ui_components/            # UI components
│   │   ├── __init__.py
│   │   ├── dashboard_controls.py # Dashboard controls implementation
│   │   └── login.py              # Authentication system
│   └── display_plots/            # Display functions
│       ├── summary_display.py    # Summary tab displays
│       ├── tod_display.py        # ToD tab displays
│       └── power_cost_display.py # Power cost analysis
│
├── backend/                      # Backend data management
│   └── data/
│       ├── __init__.py
│       └── db_data_manager.py    # Database data management
│
├── config/                       # Configuration files
│   ├── __init__.py
│   └── app_config.py            # Application configuration
│
├── db/                          # Database modules
│   ├── db_setup.py              # Database connection setup
│   ├── fetch_summary_data.py    # Summary data fetching
│   ├── fetch_tod_tab_data.py    # ToD data fetching
│   └── safe_db_utils.py         # Safe database utilities
│
├── visualizations/              # Visualization modules
│   ├── __init__.py
│   ├── summary_tab_visual.py    # Summary visualizations
│   ├── tod_tab_visual.py        # ToD visualizations
│   ├── tod_config.py            # ToD configuration
│   ├── power_cost_visual.py     # Power cost visualizations
│   ├── power_cost_calculations.py # Power cost calculations
│   └── unit_wise_power_cost_calculations.py # Unit-wise cost calculations
│
├── helper/                      # Helper utilities
│   ├── utils.py                 # Utility functions
│   └── setup_logger.py          # Logging configuration
│
└── logo/                        # Logo and branding assets
    └── logo_integrum.jpg        # Company logo
```

## Usage Guide

### 1. Authentication
- Access the login page when first launching the dashboard
- Enter your credentials to access the dashboard
- Use the logout button in the sidebar to end your session

### 2. Client Selection
- Select a client from the dropdown in the sidebar
- The system will load available plants for the selected client

### 3. Plant Selection
- **Combined View**: Leave both plant dropdowns at default to view combined data
- **Specific Plant**: Select either a solar or wind plant for detailed analysis
- **Smart Selection**: Selecting one plant type automatically resets the other

### 4. Date Selection
- Choose a date range for analysis
- **Single Day**: Select the same date for start and end
- **Multi-Day**: Select different start and end dates
- Date range is limited to ±365 days from current date

### 5. Analysis Tabs

#### Summary Tab
- **Generation vs Consumption Last 12 Months**: Long-term trend analysis
- **Generation vs Consumption Hourly**: Hourly pattern analysis
- **ToD 15m/60m/1d**: Time-of-day analysis with multiple time intervals
- **Unit-wise Monthly Bill Analysis**: Detailed billing breakdown with cost analysis

#### Generation and Consumption Tab
- **Monthly - Last 12 months TODwise**: Settlement heatmap visualization
- **Monthly TOD**: Monthly time-of-day generation vs consumption comparison
- **Daily TOD**: Daily time-of-day line chart analysis

#### Bill Tab
- **Power Cost Analysis**: Comprehensive financial analysis
- **Cost Breakdown**: Detailed billing components
- **Rate Analysis**: Grid vs renewable energy rate comparisons

## Configuration

### Database Configuration
Update `db/db_setup.py` with your database credentials:
```python
CONN = setup_db_connection(
    host="your_host",
    user="your_username", 
    password="your_password",
    database="your_database"
)
```

### Application Configuration
Modify `config/app_config.py` to customize:
- Colors and themes
- Feature flags
- UI messages
- Date range limits

## Database Schema Requirements

The dashboard expects the following database structure:

### settlement_data table
```sql
CREATE TABLE settlement_data (
    id INT PRIMARY KEY AUTO_INCREMENT,
    client_name VARCHAR(255),
    date DATE,
    datetime DATETIME,
    allocated_generation DECIMAL(10,2),
    consumption DECIMAL(10,2),
    deficit DECIMAL(10,2),
    surplus_demand DECIMAL(10,2),
    surplus_generation DECIMAL(10,2),
    settled DECIMAL(10,2)
);
```

## Database Migration

### Dumping Data from Local to EC2

To transfer your local database to an EC2 instance, follow these steps:

1. **Create a database dump from your local MySQL**
   ```bash
   # General format
   mysqldump -u your_local_user -p your_local_db_name > local_dump.sql
   
   # For this project specifically
   mysqldump -u root -p energy_db > local_dump.sql
   ```

2. **Transfer the dump file to EC2 using SCP**
   ```bash
   scp -i "D:\Harikrishnan\AWS PEM KEY\integrum_ssh_key.pem" "C:\Users\<USER>\local_dump.sql" ubuntu@3.108.243.75:/tmp/
   ```

3. **Import the dump into the target database on EC2**
   ```bash
   # SSH into your EC2 instance first, then run:
   mysql -u root -p your_target_db_name < /tmp/local_dump.sql
   ```

### Prerequisites for Database Migration
- Ensure you have the SSH key file (`integrum_ssh_key.pem`) with proper permissions
- Verify that the EC2 instance is running and accessible
- Make sure MySQL is installed and running on the EC2 instance
- Create the target database on EC2 before importing the dump

### Security Notes
- Keep your SSH key file secure and never share it
- Use strong passwords for database access
- Consider using environment variables for sensitive credentials
- Regularly backup your database before making changes

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check database credentials in `db/db_setup.py`
   - Ensure MySQL server is running
   - Verify database exists and is accessible

2. **No Client Data Available**
   - Check if `settlement_data` table has data
   - Verify `client_name` column is not null
   - Check database connection

3. **Import Errors**
   - Ensure all dependencies are installed: `pip install -r requirements.txt`
   - Check Python path and module imports

4. **Streamlit Not Starting**
   - Verify Streamlit is installed: `pip install streamlit`
   - Check if port 8501 is available
   - Try running with different port: `streamlit run app.py --server.port 8502`

5. **Authentication Issues**
   - Check login credentials configuration
   - Verify session state is properly initialized
   - Clear browser cache if login persists after logout

6. **Logo/Assets Not Loading**
   - Ensure logo files are in the `logo/` directory
   - Check file permissions and paths



## Development

### Adding New Features

1. **New Visualization**
   - Add visualization function to appropriate module in `visualizations/`
   - Create display function in `frontend/display_plots/`
   - Add UI controls in `frontend/ui_components/dashboard_controls.py`
   - Update main app in `app.py`

2. **New Data Source**
   - Add fetch function to appropriate module in `db/`
   - Update data manager in `backend/data/db_data_manager.py`
   - Add configuration in `config/app_config.py`
   - Use safe database utilities from `db/safe_db_utils.py`

3. **UI Improvements**
   - Update CSS in `frontend/ui_components/dashboard_controls.py`
   - Modify colors and themes in `config/app_config.py`
   - Add new UI components as needed
   - Update authentication in `frontend/ui_components/login.py`

4. **Authentication Enhancements**
   - Modify login logic in `frontend/ui_components/login.py`
   - Update session management
   - Add new user roles or permissions

### Code Style
- Follow PEP 8 Python style guidelines
- Use type hints where appropriate
- Add docstrings to functions and classes
- Include error handling and logging

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For technical support or feature requests:
- Create an issue in the project repository
- Contact the development team
- Check the troubleshooting guide above
