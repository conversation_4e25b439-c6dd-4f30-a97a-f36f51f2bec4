import pandas as pd
from datetime import datetime, timedelta
from db.safe_db_utils import safe_read_sql
from helper.setup_logger import setup_logger



logging = setup_logger("fetch_tod_tab_data", "fetch_tod_tab_data.log")


##ToD Generation vs Consumption
def fetch_tod_binned_data(conn, client_name: str, start_date: str, end_date: str = None) -> pd.DataFrame:
    """
    Fetch ToD-binned generation and consumption data from MySQL using mysql.connector.

    Args:
        conn: mysql.connector connection object.
        plant_id (str): Plant ID to filter on.
        start_date (str): Start date (YYYY-MM-DD).
        end_date (str, optional): End date (YYYY-MM-DD). If None, uses only start_date.

    Returns:
        pd.DataFrame: Data grouped by slot_name.
    """
    cursor = None
    try:
        cursor = conn.cursor(dictionary=True)

        if not end_date:
            end_date = start_date

        query = """
            SELECT 
                slot_name AS slot,
                SUM(allocated_generation) AS generation_kwh,
                SUM(consumption) AS consumption_kwh
            FROM 
                settlement_data
            WHERE 
                client_name = %s
                AND date BETWEEN %s AND %s
            GROUP BY 
                slot_name
            ORDER BY 
                slot_name;
        """

        cursor.execute(query, (client_name, start_date, end_date))
        results = cursor.fetchall()
        
        # Consume all results to avoid "Unread result found" error
        while cursor.nextset():
            pass
            
        return pd.DataFrame(results)
        
    except Exception as e:
        print(f"Error in fetch_tod_binned_data: {e}")
        return pd.DataFrame()
    finally:
        if cursor:
            cursor.close()




##ToD Generation AND Consumption
def fetch_daily_tod_data(
    conn,
    client_name: str,
    start_date: str,
    end_date: str,
    plant_type: str = None
) -> pd.DataFrame:
    """
    Fetch daily ToD-binned generation and consumption data using slot_name and date directly.

    Returns:
        pd.DataFrame with columns: date, slot, generation_kwh, consumption_kwh
    """
    query = """
        SELECT
            date,
            slot_name AS slot,
            SUM(allocated_generation) AS generation_kwh,
            SUM(consumption) AS consumption_kwh
        FROM
            settlement_data
        WHERE
            client_name = %s
            AND date BETWEEN %s AND %s
    """

    params = [client_name, start_date, end_date]

    if plant_type:
        query += " AND type = %s"
        params.append(plant_type)

    query += """
        GROUP BY date, slot_name
        ORDER BY date, slot_name;
    """

    cursor = None
    try:
        cursor = conn.cursor(dictionary=True)
        cursor.execute(query, params)
        rows = cursor.fetchall()
        
        # Consume all results to avoid "Unread result found" error
        while cursor.nextset():
            pass

        return pd.DataFrame(rows)

    except Exception as e:
        print(f"Error in fetch_daily_tod_data: {e}")
        return pd.DataFrame(columns=["date", "slot", "generation_kwh", "consumption_kwh"])
    finally:
        if cursor:
            cursor.close()




##Monthly ToD Before Banking


def fetch_all_daily_tod_data(
    conn,
    client_name: str,
    plant_type: str = None
) -> pd.DataFrame:
    """
    Fetch all available daily ToD-binned generation and consumption data
    grouped by date and slot_name, without any date filtering.

    Returns:
        pd.DataFrame with columns: date, slot, generation_kwh, consumption_kwh
    """
    query = """
        SELECT
            date,
            slot_name AS slot,
            SUM(allocated_generation) AS generation_kwh,
            SUM(consumption) AS consumption_kwh
        FROM
            settlement_data
        WHERE
            client_name = %s
    """

    params = [client_name]

    if plant_type:
        query += " AND type = %s"
        params.append(plant_type)

    query += """
        GROUP BY date, slot_name
        ORDER BY date, FIELD(slot_name, 'Morning Peak', 'Day (Normal)', 'Evening Peak', 'Off-Peak');
    """

    cursor = None
    try:
        cursor = conn.cursor(dictionary=True)
        cursor.execute(query, params)
        rows = cursor.fetchall()
        
        # Consume all results to avoid "Unread result found" error
        while cursor.nextset():
            pass

        return pd.DataFrame(rows)

    except Exception as e:
        print("⚠️ Error fetching data:", e)
        return pd.DataFrame(columns=["date", "slot", "generation_kwh", "consumption_kwh"])
    finally:
        if cursor:
            cursor.close()




##Monthly Banking Settlement
def fetch_combined_monthly_data(
    conn,
    plant_name: str = None,
    cons_unit: str = None
) -> pd.DataFrame:
    """
    Fetch monthly aggregated data for both consumption and banking settlement.

    Args:
        conn: MySQL connection object
        plant_name (str, optional): Filter by plant name
        cons_unit (str, optional): Filter by consumption unit

    Returns:
        pd.DataFrame: Merged DataFrame with month-wise consumption and settlement data
    """

    # --- Fetch Daily Consumption ---
    consumption_query = """
        SELECT
            date,
            consumption,
            allocated_generation AS generation
        FROM
            settlement_data
        WHERE
            date IS NOT NULL
            {plant_filter}
        ORDER BY
            date;
    """

    # --- Fetch Monthly Banking Settlement ---
    settlement_query = """
        SELECT
            date AS month,
            SUM(matched_settled_sum) AS total_matched_settled_sum,
            SUM(intra_settlement) AS total_intra_settlement,
            SUM(inter_settlement) AS total_inter_settlement,
            SUM(surplus_demand_sum) AS surplus_demand_sum,
            SUM(surplus_generation_sum) AS surplus_generation_sum,
            SUM(surplus_demand_sum_after_inter) AS surplus_demand_sum_after_inter
        FROM
            banking_settlement
        WHERE
            date IS NOT NULL
            {plant_filter}
        GROUP BY
            date
        ORDER BY
            date;
    """

    # Prepare query and params
    params = ()
    plant_filter = ""
    
    if plant_name:
        plant_filter += "AND client_name = %s"
        params = (plant_name,)
    
    if cons_unit:
        plant_filter += " AND cons_unit = %s"
        params = params + (cons_unit,) if params else (cons_unit,)
    
    consumption_query = consumption_query.format(plant_filter=plant_filter)
    settlement_query = settlement_query.format(plant_filter=plant_filter)

    try:
        # Read consumption data and group by month using safe database utility
        df_consumption = safe_read_sql(consumption_query, conn, params)
        
        if df_consumption.empty:
            print("Warning: No consumption data found")
            return pd.DataFrame()
            
        df_consumption['date'] = pd.to_datetime(df_consumption['date'])
        df_consumption['month'] = df_consumption['date'].dt.to_period('M').astype(str)
        # df_consumption_monthly = (
        #     df_consumption.groupby('month', as_index=False)['consumption']
        #     .sum()
        #     .rename(columns={'consumption': 'total_consumption_sum'})
        # )

        df_consumption_monthly = (
            df_consumption
            .groupby('month', as_index=False)[['consumption', 'generation']]
            .sum()
            .rename(columns={
                'consumption': 'total_consumption_sum',
                'generation': 'total_generation_sum'
            })
        )


        # Read banking settlement data (already monthly) using safe database utility
        df_settlement = safe_read_sql(settlement_query, conn, params)
        
        if df_settlement.empty:
            print("Warning: No settlement data found")
            # Return consumption data only with zeros for settlement columns
            df_consumption_monthly['total_matched_settled_sum'] = 0
            df_consumption_monthly['total_intra_settlement'] = 0
            df_consumption_monthly['total_inter_settlement'] = 0
            return df_consumption_monthly
            
        df_settlement['month'] = pd.to_datetime(df_settlement['month']).dt.to_period('M').astype(str)

        # Merge both on 'month'
        df_combined = pd.merge(
            df_consumption_monthly,
            df_settlement,
            on='month',
            how='outer'
        ).sort_values('month').reset_index(drop=True)

        return df_combined
        
    except Exception as e:
        print(f"Error in fetch_combined_monthly_data: {e}")
        return pd.DataFrame()




def fetch_hourly_generation_data(
    conn,
    client_name: str,
    start_date: str,
    end_date: str = None
) -> pd.DataFrame:
    """
    Fetch 15-minute generation data and aggregate to hourly intervals.
    
    Args:
        conn: mysql.connector connection object
        client_name (str): Client name to filter on
        start_date (str): Start date (YYYY-MM-DD)
        end_date (str, optional): End date (YYYY-MM-DD). If None, uses only start_date
    
    Returns:
        pd.DataFrame: Hourly aggregated data with columns: date, datetime, hour, generation_kwh
    """
    cursor = None
    try:
        cursor = conn.cursor(dictionary=True)
        
        if not end_date:
            end_date = start_date
            
        query = """
            SELECT 
                date,
                datetime,
                allocated_generation AS generation_kwh
            FROM 
                settlement_data
            WHERE 
                client_name = %s
                AND date BETWEEN %s AND %s
                AND datetime IS NOT NULL
                AND allocated_generation IS NOT NULL
            ORDER BY 
                date, datetime;
        """
        
        cursor.execute(query, (client_name, start_date, end_date))
        results = cursor.fetchall()
        
        # Consume all results to avoid "Unread result found" error
        while cursor.nextset():
            pass
            
        df = pd.DataFrame(results)
        
        if df.empty:
            logging.warning(f"No hourly generation data found for client: {client_name}")
            return pd.DataFrame(columns=['date', 'datetime', 'hour', 'generation_kwh'])
        
        # Convert datetime to proper datetime format
        df['datetime'] = pd.to_datetime(df['datetime'])
        df['date'] = pd.to_datetime(df['date'])
        
        # Extract hour from datetime
        df['hour'] = df['datetime'].dt.hour
        
        # Aggregate 15-minute data to hourly
        df_hourly = df.groupby([
            df['date'].dt.date,
            df['hour']
        ]).agg({
            'generation_kwh': 'sum'
        }).reset_index()
        
        # Rename columns for clarity
        df_hourly.columns = ['date', 'hour', 'generation_kwh']
        
        # Create full datetime for plotting
        df_hourly['datetime'] = pd.to_datetime(df_hourly['date'].astype(str) + ' ' + df_hourly['hour'].astype(str) + ':00:00')
        
        # Create time string for grouping (HH:MM format)
        df_hourly['time'] = df_hourly['hour'].apply(lambda x: f"{x:02d}:00")
        
        logging.info(f"Successfully aggregated {len(df)} 15-minute records to {len(df_hourly)} hourly records for {client_name}")
        
        return df_hourly
        
    except Exception as e:
        logging.error(f"Error in fetch_hourly_generation_data: {str(e)}")
        return pd.DataFrame(columns=['date', 'datetime', 'hour', 'generation_kwh', 'time'])
    finally:
        if cursor:
            cursor.close()


def fetch_monthly_banking_calculations(
    conn,
    plant_name: str = None
) -> pd.DataFrame:
    """
    Fetch monthly aggregated data for consumption, banking settlement, and surplus demand.

    Args:
        conn: MySQL connection object
        plant_name (str, optional): Filter by plant name

    Returns:
        pd.DataFrame: DataFrame with month-wise merged metrics including surplus_demand_after_banking
    """

    # --- Fetch Daily Consumption ---
    consumption_query = """
        SELECT
            date,
            consumption
        FROM
            settlement_data
        WHERE
            date IS NOT NULL
            {plant_filter}
        ORDER BY
            date;
    """

    # --- Fetch Monthly Banking Settlement (with surplus_demand_sum) ---
    settlement_query = """
        SELECT
            date AS month,
            SUM(matched_settled_sum) AS matched_settled_sum,
            SUM(intra_settlement) AS intra_settlement,
            SUM(inter_settlement) AS inter_settlement,
            SUM(surplus_demand_sum) AS surplus_demand_sum
        FROM
            banking_settlement
        WHERE
            date IS NOT NULL
            {plant_filter}
        GROUP BY
            date
        ORDER BY
            date;
    """

    # Prepare queries and parameters
    params = ()
    if plant_name:
        filter_clause = "AND client_name = %s"
        params = (plant_name,)
    else:
        filter_clause = ""

    consumption_query = consumption_query.format(plant_filter=filter_clause)
    settlement_query = settlement_query.format(plant_filter=filter_clause)

    # --- Fetch and process consumption ---
    df_consumption = pd.read_sql(consumption_query, con=conn, params=params)
    df_consumption['date'] = pd.to_datetime(df_consumption['date'])
    df_consumption['month'] = df_consumption['date'].dt.to_period('M').astype(str)
    df_consumption_monthly = (
        df_consumption.groupby('month', as_index=False)['consumption']
        .sum()
        .rename(columns={'consumption': 'total_consumption_sum'})
    )

    # --- Fetch and process settlement data ---
    df_settlement = pd.read_sql(settlement_query, con=conn, params=params)
    df_settlement['month'] = pd.to_datetime(df_settlement['month']).dt.to_period('M').astype(str)

    # --- Merge and calculate surplus_demand_after_banking ---
    df_combined = pd.merge(df_consumption_monthly, df_settlement, on='month', how='outer')
    df_combined = df_combined.sort_values('month').reset_index(drop=True)

    df_combined['surplus_demand_after_banking'] = (
    df_combined['surplus_demand_sum'].fillna(0)
    - df_combined['matched_settled_sum'].fillna(0)
    - df_combined['intra_settlement'].fillna(0)
    - df_combined['inter_settlement'].fillna(0)
    ).clip(lower=0)

    return df_combined


def fetch_available_cons_units(
    conn,
    client_name: str
) -> list:
    """
    Fetch all available cons_unit values for a specific client.
    
    Args:
        conn: MySQL connection object
        client_name: Client name to filter by
        
    Returns:
        List of available cons_unit values
    """
    cursor = None
    try:
        cursor = conn.cursor(dictionary=True)
        
        query = """
            SELECT DISTINCT cons_unit
            FROM settlement_data
            WHERE client_name = %s
              AND cons_unit IS NOT NULL
            ORDER BY cons_unit;
        """
        
        cursor.execute(query, (client_name,))
        results = cursor.fetchall()
        
        # Consume all results to avoid "Unread result found" error
        while cursor.nextset():
            pass
            
        return [row['cons_unit'] for row in results]
        
    except Exception as e:
        logging.error(f"Error fetching cons_units for {client_name}: {e}")
        return []
    finally:
        if cursor:
            cursor.close()


def fetch_heatmap_data(
    conn,
    client_name: str,
    start_date: str = None,
    end_date: str = None,
    plant_type: str = None
) -> pd.DataFrame:
    """
    Fetch data for generation vs consumption heatmap with settled values and ToD slots.

    Args:
        conn: Database connection
        client_name: Name of the client/plant
        start_date: Start date (optional, defaults to last 12 months)
        end_date: End date (optional, defaults to current date)
        plant_type: Plant type filter (optional)

    Returns:
        pd.DataFrame with columns: date, slot_name, generation_kwh, consumption_kwh, settled
    """
    try:
        # Default to last 12 months if no dates provided
        if not end_date:
            end_date = datetime.now().strftime('%Y-%m-%d')
        if not start_date:
            start_date = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')

        query = """
            SELECT
                date,
                slot_name,
                SUM(allocated_generation) AS generation_kwh,
                SUM(consumption) AS consumption_kwh,
                SUM(settled) AS settled
            FROM
                settlement_data
            WHERE
                client_name = %s
                AND date BETWEEN %s AND %s
        """

        params = [client_name, start_date, end_date]

        if plant_type:
            query += " AND type = %s"
            params.append(plant_type)

        query += """
            GROUP BY date, slot_name
            ORDER BY date, FIELD(slot_name, 'Morning Peak', 'Day (Normal)', 'Evening Peak', 'Off-Peak');
        """

        df = safe_read_sql(query, conn, params)

        if df.empty:
            logging.warning(f"No heatmap data found for {client_name}")
            return pd.DataFrame()

        # Convert date to datetime
        df['date'] = pd.to_datetime(df['date'])

        logging.info(f"Successfully fetched {len(df)} rows of heatmap data for {client_name}")
        return df

    except Exception as e:
        logging.error(f"Error fetching heatmap data for {client_name}: {str(e)}")
        return pd.DataFrame()






# ##ToD Generation vs Consumption
# def fetch_tod_binned_data(conn, client_name: str, start_date: str, end_date: str = None) -> pd.DataFrame:
#     """
#     Fetch ToD-binned generation and consumption data from MySQL using mysql.connector.

#     Args:
#         conn: mysql.connector connection object.
#         plant_id (str): Plant ID to filter on.
#         start_date (str): Start date (YYYY-MM-DD).
#         end_date (str, optional): End date (YYYY-MM-DD). If None, uses only start_date.

#     Returns:
#         pd.DataFrame: Data grouped by slot_name.
#     """
#     cursor = None
#     try:
#         cursor = conn.cursor(dictionary=True)

#         if not end_date:
#             end_date = start_date

#         query = """
#             SELECT 
#                 slot_name AS slot,
#                 SUM(allocated_generation) AS generation_kwh,
#                 SUM(consumption) AS consumption_kwh
#             FROM 
#                 settlement_data
#             WHERE 
#                 client_name = %s
#                 AND date BETWEEN %s AND %s
#             GROUP BY 
#                 slot_name
#             ORDER BY 
#                 slot_name;
#         """

#         cursor.execute(query, (client_name, start_date, end_date))
#         results = cursor.fetchall()
        
#         # Consume all results to avoid "Unread result found" error
#         while cursor.nextset():
#             pass
            
#         return pd.DataFrame(results)
        
#     except Exception as e:
#         logging.error(f"Error in fetch_tod_binned_data: {e}")
#         return pd.DataFrame()
#     finally:
#         if cursor:
#             cursor.close()




# ##ToD Generation AND Consumption
# def fetch_daily_tod_data(
#     conn,
#     client_name: str,
#     start_date: str,
#     end_date: str,
#     plant_type: str = None
# ) -> pd.DataFrame:
#     """
#     Fetch daily ToD-binned generation and consumption data using slot_name and date directly.

#     Returns:
#         pd.DataFrame with columns: date, slot, generation_kwh, consumption_kwh
#     """
#     query = """
#         SELECT
#             date,
#             slot_name AS slot,
#             SUM(allocated_generation) AS generation_kwh,
#             SUM(consumption) AS consumption_kwh
#         FROM
#             settlement_data
#         WHERE
#             client_name = %s
#             AND date BETWEEN %s AND %s
#     """

#     params = [client_name, start_date, end_date]

#     if plant_type:
#         query += " AND type = %s"
#         params.append(plant_type)

#     query += """
#         GROUP BY date, slot_name
#         ORDER BY date, slot_name;
#     """

#     cursor = None
#     try:
#         cursor = conn.cursor(dictionary=True)
#         cursor.execute(query, params)
#         rows = cursor.fetchall()
        
#         # Consume all results to avoid "Unread result found" error
#         while cursor.nextset():
#             pass

#         return pd.DataFrame(rows)

#     except Exception as e:
#         logging.error(f"Error in fetch_daily_tod_data: {e}")
#         return pd.DataFrame(columns=["date", "slot", "generation_kwh", "consumption_kwh"])
#     finally:
#         if cursor:
#             cursor.close()




# ##Monthly ToD Before Banking


# def fetch_all_daily_tod_data(
#     conn,
#     client_name: str,
#     plant_type: str = None
# ) -> pd.DataFrame:
#     """
#     Fetch all available daily ToD-binned generation and consumption data
#     grouped by date and slot_name, without any date filtering.

#     Returns:
#         pd.DataFrame with columns: date, slot, generation_kwh, consumption_kwh
#     """
#     query = """
#         SELECT
#             date,
#             slot_name AS slot,
#             SUM(allocated_generation) AS generation_kwh,
#             SUM(consumption) AS consumption_kwh
#         FROM
#             settlement_data
#         WHERE
#             client_name = %s
#     """

#     params = [client_name]

#     if plant_type:
#         query += " AND type = %s"
#         params.append(plant_type)

#     query += """
#         GROUP BY date, slot_name
#         ORDER BY date, FIELD(slot_name, 'Morning Peak', 'Day (Normal)', 'Evening Peak', 'Off-Peak');
#     """

#     cursor = None
#     try:
#         cursor = conn.cursor(dictionary=True)
#         cursor.execute(query, params)
#         rows = cursor.fetchall()
        
#         # Consume all results to avoid "Unread result found" error
#         while cursor.nextset():
#             pass

#         return pd.DataFrame(rows)

#     except Exception as e:
#         logging.error("⚠️ Error fetching data:", e)
#         return pd.DataFrame(columns=["date", "slot", "generation_kwh", "consumption_kwh"])
#     finally:
#         if cursor:
#             cursor.close()




# ##Monthly Banking Settlement
# def fetch_combined_monthly_data(
#     conn,
#     plant_name: str = None
# ) -> pd.DataFrame:
#     """
#     Fetch monthly aggregated data for both consumption and banking settlement.

#     Args:
#         conn: MySQL connection object
#         plant_name (str, optional): Filter by plant name

#     Returns:
#         pd.DataFrame: Merged DataFrame with month-wise consumption and settlement data
#     """

#     # --- Fetch Daily Consumption ---
#     consumption_query = """
#         SELECT
#             date,
#             consumption,
#             allocated_generation AS generation
#         FROM
#             settlement_data
#         WHERE
#             date IS NOT NULL
#             {plant_filter}
#         ORDER BY
#             date;
#     """

#     # --- Fetch Monthly Banking Settlement ---
#     settlement_query = """
#         SELECT
#             date AS month,
#             SUM(matched_settled_sum) AS total_matched_settled_sum,
#             SUM(intra_settlement) AS total_intra_settlement,
#             SUM(inter_settlement) AS total_inter_settlement,
#             SUM(surplus_demand_sum) AS surplus_demand_sum
#         FROM
#             banking_settlement
#         WHERE
#             date IS NOT NULL
#             {plant_filter}
#         GROUP BY
#             date
#         ORDER BY
#             date;
#     """

#     # Prepare query and params
#     params = ()
#     if plant_name:
#         consumption_query = consumption_query.format(plant_filter="AND client_name = %s")
#         settlement_query = settlement_query.format(plant_filter="AND client_name = %s")
#         params = (plant_name,)
#     else:
#         consumption_query = consumption_query.format(plant_filter="")
#         settlement_query = settlement_query.format(plant_filter="")

#     try:
#         # Read consumption data and group by month using safe database utility
#         df_consumption = safe_read_sql(consumption_query, conn, params)
        
#         if df_consumption.empty:
#             print("Warning: No consumption data found")
#             return pd.DataFrame()
            
#         df_consumption['date'] = pd.to_datetime(df_consumption['date'])
#         df_consumption['month'] = df_consumption['date'].dt.to_period('M').astype(str)
#         # df_consumption_monthly = (
#         #     df_consumption.groupby('month', as_index=False)['consumption']
#         #     .sum()
#         #     .rename(columns={'consumption': 'total_consumption_sum'})
#         # )

#         df_consumption_monthly = (
#             df_consumption
#             .groupby('month', as_index=False)[['consumption', 'generation']]
#             .sum()
#             .rename(columns={
#                 'consumption': 'total_consumption_sum',
#                 'generation': 'total_generation_sum'
#             })
#         )


#         # Read banking settlement data (already monthly) using safe database utility
#         df_settlement = safe_read_sql(settlement_query, conn, params)
        
#         if df_settlement.empty:
#             print("Warning: No settlement data found")
#             # Return consumption data only with zeros for settlement columns
#             df_consumption_monthly['total_matched_settled_sum'] = 0
#             df_consumption_monthly['total_intra_settlement'] = 0
#             df_consumption_monthly['total_inter_settlement'] = 0
#             return df_consumption_monthly
            
#         df_settlement['month'] = pd.to_datetime(df_settlement['month']).dt.to_period('M').astype(str)

#         # Merge both on 'month'
#         df_combined = pd.merge(
#             df_consumption_monthly,
#             df_settlement,
#             on='month',
#             how='outer'
#         ).sort_values('month').reset_index(drop=True)

#         return df_combined
        
#     except Exception as e:
#         logging.error(f"Error in fetch_combined_monthly_data: {e}")
#         return pd.DataFrame()


def fetch_tod_data_with_granularity(
    conn,
    client_name: str,
    start_date: str,
    end_date: str,
    granularity: str = "daily"
) -> pd.DataFrame:
    """
    Fetch ToD data with specified granularity for line chart visualization.
    
    Args:
        conn: mysql.connector connection object
        client_name: Client name to filter on
        start_date: Start date (YYYY-MM-DD)
        end_date: End date (YYYY-MM-DD)
        granularity: "15min", "60min", or "daily"
    
    Returns:
        pd.DataFrame: Data with columns: datetime/date, slot, generation_kwh, consumption_kwh
    """
    cursor = None
    try:
        cursor = conn.cursor(dictionary=True)
        
        if granularity == "15min":
            # Get 15-minute data with datetime
            query = """
                SELECT 
                    datetime,
                    slot_name AS slot,
                    allocated_generation AS generation_kwh,
                    consumption AS consumption_kwh
                FROM 
                    settlement_data
                WHERE 
                    client_name = %s
                    AND date BETWEEN %s AND %s
                    AND datetime IS NOT NULL
                    AND slot_name IS NOT NULL
                ORDER BY 
                    datetime, slot_name;
            """
            
        elif granularity == "60min":
            # Get hourly aggregated data
            query = """
                SELECT 
                    DATE_FORMAT(datetime, '%Y-%m-%d %H:00:00') AS datetime,
                    slot_name AS slot,
                    SUM(allocated_generation) AS generation_kwh,
                    SUM(consumption) AS consumption_kwh
                FROM 
                    settlement_data
                WHERE 
                    client_name = %s
                    AND date BETWEEN %s AND %s
                    AND datetime IS NOT NULL
                    AND slot_name IS NOT NULL
                GROUP BY 
                    DATE_FORMAT(datetime, '%Y-%m-%d %H:00:00'), slot_name
                ORDER BY 
                    datetime, slot_name;
            """
            
        else:  # daily
            # Get daily aggregated data
            query = """
                SELECT 
                    date,
                    slot_name AS slot,
                    SUM(allocated_generation) AS generation_kwh,
                    SUM(consumption) AS consumption_kwh
                FROM 
                    settlement_data
                WHERE 
                    client_name = %s
                    AND date BETWEEN %s AND %s
                    AND slot_name IS NOT NULL
                GROUP BY 
                    date, slot_name
                ORDER BY 
                    date, slot_name;
            """
        
        cursor.execute(query, (client_name, start_date, end_date))
        results = cursor.fetchall()
        
        # Consume all results to avoid "Unread result found" error
        while cursor.nextset():
            pass
            
        df = pd.DataFrame(results)
        
        if df.empty:
            logging.warning(f"No ToD data found for client: {client_name} with {granularity} granularity")
            return pd.DataFrame(columns=['datetime' if granularity != 'daily' else 'date', 'slot', 'generation_kwh', 'consumption_kwh'])
        
        # Convert datetime/date columns to proper format
        if granularity != "daily":
            df['datetime'] = pd.to_datetime(df['datetime'])
        else:
            df['date'] = pd.to_datetime(df['date'])
            
        logging.info(f"Successfully fetched {len(df)} ToD records for {client_name} with {granularity} granularity")
        return df
        
    except Exception as e:
        logging.error(f"Error in fetch_tod_data_with_granularity: {str(e)}")
        return pd.DataFrame(columns=['datetime' if granularity != 'daily' else 'date', 'slot', 'generation_kwh', 'consumption_kwh'])
    finally:
        if cursor:
            cursor.close()


def fetch_datetime_tod_data(conn, client_name: str, start_date: str = None, end_date: str = None) -> pd.DataFrame:
    """
    Fetch ToD data with datetime granularity (no aggregation) for heatmap display.
    
    Args:
        conn: MySQL connection object
        client_name (str): Client name to filter data
        start_date (str, optional): Start date filter (YYYY-MM-DD)
        end_date (str, optional): End date filter (YYYY-MM-DD)
        
    Returns:
        pd.DataFrame: Data with columns: datetime, slot, generation_kwh, consumption_kwh
    """
    cursor = None
    try:
        cursor = conn.cursor(dictionary=True)
        
        # Base query to get raw datetime data without aggregation
        query = """
            SELECT 
                datetime,
                slot_name AS slot,
                allocated_generation AS generation_kwh,
                consumption AS consumption_kwh
            FROM 
                settlement_data
            WHERE 
                client_name = %s
                AND datetime IS NOT NULL
                AND slot_name IS NOT NULL
        """
        
        params = [client_name]
        
        # Add date filters if provided
        if start_date and end_date:
            query += " AND date BETWEEN %s AND %s"
            params.extend([start_date, end_date])
        elif start_date:
            query += " AND date >= %s"
            params.append(start_date)
        elif end_date:
            query += " AND date <= %s"
            params.append(end_date)
        
        query += """
            ORDER BY 
                datetime, slot_name;
        """
        
        cursor.execute(query, params)
        results = cursor.fetchall()
        
        # Consume all results to avoid "Unread result found" error
        while cursor.nextset():
            pass
            
        df = pd.DataFrame(results)
        
        if df.empty:
            logging.warning(f"No datetime ToD data found for client: {client_name}")
            return pd.DataFrame(columns=['datetime', 'slot', 'generation_kwh', 'consumption_kwh'])
        
        # Convert datetime column to proper format
        df['datetime'] = pd.to_datetime(df['datetime'])
        
        logging.info(f"Successfully fetched {len(df)} datetime ToD records for {client_name}")
        return df
        
    except Exception as e:
        logging.error(f"Error in fetch_datetime_tod_data: {str(e)}")
        return pd.DataFrame(columns=['datetime', 'slot', 'generation_kwh', 'consumption_kwh'])
    finally:
        if cursor:
            cursor.close()

